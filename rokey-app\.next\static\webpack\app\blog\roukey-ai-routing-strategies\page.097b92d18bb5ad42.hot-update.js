"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/roukey-ai-routing-strategies/page",{

/***/ "(app-pages-browser)/./src/app/blog/roukey-ai-routing-strategies/page.tsx":
/*!************************************************************!*\
  !*** ./src/app/blog/roukey-ai-routing-strategies/page.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RouKeyAIRoutingStrategies)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction RouKeyAIRoutingStrategies() {\n    const post = {\n        title: 'RouKey\\'s Intelligent AI Routing: How We Achieved 99.9% Uptime with Multi-Provider Fallbacks',\n        author: 'David Okoro',\n        date: '2025-01-10',\n        readTime: '10 min read',\n        category: 'Product Deep Dive',\n        tags: [\n            'RouKey',\n            'AI Routing',\n            'Fault Tolerance',\n            'Infrastructure',\n            'Reliability'\n        ],\n        excerpt: 'Behind the scenes of RouKey\\'s intelligent routing system. Learn how we built fault-tolerant AI infrastructure that automatically routes to the best-performing models.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://plus.unsplash.com/premium_photo-*************-252eab5fd2db?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"RouKey's Intelligent Routing - Network communications with connecting lines and dots\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"RouKey's Intelligent Routing System\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Building a reliable AI API gateway that maintains 99.9% uptime while routing between 300+ AI models across multiple providers is no small feat. In this deep dive, I'll share the technical architecture and strategies that power RouKey's intelligent routing system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The Challenge: AI Provider Reliability\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: 'When we started building RouKey, we quickly realized that individual AI providers have varying reliability patterns. OpenAI might have rate limits during peak hours, Anthropic could experience regional outages, and smaller providers might have inconsistent response times. Our users needed a solution that \"just works\" regardless of these underlying issues.'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCCA Reliability Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: \"Individual AI providers typically achieve 95-98% uptime. RouKey's multi-provider routing achieves 99.9% uptime by intelligently failing over between providers.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Our Intelligent Routing Architecture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"1. Real-Time Health Monitoring\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Every AI provider in our network is continuously monitored for:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Response Time:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Average latency over the last 5 minutes\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Success Rate:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Percentage of successful requests\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Rate Limit Status:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Current rate limit utilization\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Error Patterns:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Types and frequency of errors\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Regional Performance:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Performance by geographic region\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"2. Multi-Tier Fallback Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Our routing system implements a sophisticated fallback hierarchy:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary Route:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Best-performing model for the specific task\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary Route:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Alternative model with similar capabilities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Tertiary Route:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Different provider with comparable performance\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Emergency Route:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Fastest available model for basic functionality\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"3. Intelligent Request Classification\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Before routing, every request is classified to determine the optimal model:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Complexity Analysis:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Simple vs. complex reasoning requirements\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Domain Detection:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Code, creative writing, analysis, etc.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Length Requirements:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Short responses vs. long-form content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Latency Sensitivity:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Real-time vs. batch processing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border-l-4 border-green-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-green-900 mb-2\",\n                                                        children: \"\\uD83D\\uDE80 Performance Impact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-800\",\n                                                        children: \"Intelligent classification reduces average response time by 35% by routing simple queries to faster models and complex queries to more capable models.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Technical Implementation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Circuit Breaker Pattern\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"We implement circuit breakers for each AI provider to prevent cascading failures:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Closed State:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Normal operation, requests flow through\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Open State:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Provider is failing, requests are routed elsewhere\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Half-Open State:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Testing if provider has recovered\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Adaptive Load Balancing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Our load balancer adapts in real-time based on:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Current Load:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Distribute requests based on provider capacity\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Historical Performance:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Weight routing based on past reliability\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Cost Optimization:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Factor in pricing when performance is equivalent\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Geographic Proximity:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Route to nearest available provider\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Caching Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Intelligent caching reduces load and improves response times:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Semantic Caching:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Cache based on meaning, not exact text\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"TTL Optimization:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Dynamic cache expiration based on content type\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Cache Warming:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Pre-populate cache with common queries\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Distributed Cache:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Global cache network for low latency\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Monitoring and Observability\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Real-Time Dashboards\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Our operations team monitors system health through comprehensive dashboards:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Provider Health:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Real-time status of all AI providers\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Routing Decisions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Live view of routing logic and fallbacks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Performance Metrics:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Latency, throughput, and error rates\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Cost Analytics:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Real-time cost tracking and optimization\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Automated Alerting\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Proactive alerting ensures issues are caught before they impact users:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Threshold Alerts:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Trigger when metrics exceed normal ranges\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Anomaly Detection:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" ML-powered detection of unusual patterns\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Predictive Alerts:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Early warning of potential issues\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Escalation Policies:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Automatic escalation for critical issues\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"⚡ Response Time\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800\",\n                                                        children: \"Our monitoring system detects and responds to provider issues within 30 seconds, automatically rerouting traffic to healthy providers.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Lessons Learned\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"1. Diversity is Key\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Having providers across different infrastructure stacks (AWS, GCP, Azure) significantly improves overall reliability. When one cloud provider has issues, others remain unaffected.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"2. Regional Redundancy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Geographic distribution of providers helps with both latency and reliability. Regional outages don't affect global service availability.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"3. Gradual Rollouts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"When adding new providers or routing logic, gradual rollouts with canary deployments prevent widespread issues.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Future Enhancements\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"We're continuously improving our routing system with upcoming features:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"ML-Powered Routing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Use machine learning to predict optimal routing decisions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"User-Specific Optimization:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Learn individual user preferences and optimize accordingly\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Edge Computing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Deploy routing logic closer to users for reduced latency\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Advanced Caching:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Context-aware caching that understands conversation flow\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Building a reliable AI routing system requires careful attention to monitoring, fallback strategies, and continuous optimization. By implementing these patterns, RouKey achieves industry-leading uptime while providing cost-effective access to the best AI models.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The key is to design for failure from the beginning. Assume providers will have issues, plan for various failure modes, and build systems that gracefully handle these situations. Your users will thank you for the reliability.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF Try RouKey's Routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800 mb-4\",\n                                                        children: \"Experience the reliability of RouKey's intelligent routing system. Get started with our free tier today.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors\",\n                                                        children: \"Start Free Trial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-8 text-center\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/ai-api-gateway-2025-guide\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"The Complete Guide to AI API Gateways in 2025\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Discover how AI API gateways are revolutionizing multi-model routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/ai-model-selection-guide\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"AI Model Selection Guide 2025: GPT-4, Claude, Gemini Compared\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Comprehensive comparison of leading AI models with performance benchmarks and cost analysis.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\roukey-ai-routing-strategies\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = RouKeyAIRoutingStrategies;\nvar _c;\n$RefreshReg$(_c, \"RouKeyAIRoutingStrategies\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/roukey-ai-routing-strategies/page.tsx\n"));

/***/ })

});