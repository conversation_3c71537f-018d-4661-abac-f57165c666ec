"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/ai-model-selection-guide/page",{

/***/ "(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/blog/ai-model-selection-guide/page.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIModelSelectionGuide)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction AIModelSelectionGuide() {\n    const post = {\n        title: 'Best AI Models 2025: OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro & DeepSeek R1 Compared',\n        author: 'David Okoro',\n        date: '2025-06-25',\n        readTime: '18 min read',\n        category: 'AI Comparison',\n        tags: [\n            'Best AI Models 2025',\n            'OpenAI o3',\n            'Claude 4 Opus',\n            'Gemini 2.5 Pro',\n            'DeepSeek R1',\n            'AI Benchmarks',\n            'Model Performance'\n        ],\n        excerpt: 'Comprehensive comparison of the latest AI models in 2025. Performance benchmarks, cost analysis, coding capabilities, reasoning tests, and multimodal features across OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"AI Model Selection - White robot representing artificial intelligence\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"AI Model Comparison 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape in 2025 is more diverse and capable than ever before. With over 300 models available across different providers, choosing the right model for your specific use case can be overwhelming. This comprehensive guide breaks down the leading AI models, their strengths, weaknesses, and optimal use cases.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF Quick Navigation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: \"This guide covers performance benchmarks, cost analysis, and specific recommendations for coding, creative writing, analysis, and general-purpose tasks.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Top-Tier Models: The Leaders\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"GPT-4 (OpenAI)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" General-purpose tasks, creative writing, complex reasoning\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent at following complex instructions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong creative writing capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good at maintaining context in long conversations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Reliable and consistent performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $30/1M input tokens, $60/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Claude 3.5 Sonnet (Anthropic)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Code generation, analysis, technical writing\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Superior code generation and debugging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent at analytical tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong safety and alignment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good at handling large documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $3/1M input tokens, $15/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Gemini 2.0 Flash (Google)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Fast responses, cost-effective tasks, multimodal\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Extremely fast response times\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Cost-effective for high-volume use\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good multimodal capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Reliable for simple to medium complexity tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.075/1M input tokens, $0.30/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border-l-4 border-green-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-green-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-800\",\n                                                        children: \"Use RouKey's intelligent routing to automatically select the best model for each task, optimizing for both performance and cost.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Specialized Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Code-Specialized Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Claude 3.5 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Best overall for code generation and debugging\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"GPT-4:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Good for explaining code and architectural decisions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DeepSeek Coder:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent for specific programming languages\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Creative Writing Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"GPT-4:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Best for creative storytelling and content creation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Claude 3.5 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent for technical and analytical writing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Gemini Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Good for marketing copy and social media content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Cost-Performance Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Model\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Input Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Output Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"GPT-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 199,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$30/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$60/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Excellent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 202,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Claude 3.5 Sonnet\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$3/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$15/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Excellent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Gemini 2.0 Flash\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 211,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.075/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 212,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.30/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 213,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Very Good\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Use Case Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"For Startups and Small Businesses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Gemini 2.0 Flash for cost-effective general tasks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Claude 3.5 Sonnet for complex analysis and code\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Fallback:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" GPT-4 for tasks requiring highest quality\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"For Enterprise Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Claude 3.5 Sonnet for reliability and safety\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" GPT-4 for creative and complex reasoning tasks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"High-volume:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Gemini 2.0 Flash for cost optimization\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Future Trends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape continues to evolve rapidly. Key trends to watch:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Specialized Models:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" More domain-specific models for niche use cases\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Cost Reduction:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Continued price competition driving costs down\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Multimodal Capabilities:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Better integration of text, image, and audio\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Edge Deployment:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Smaller models optimized for local deployment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83D\\uDE80 Get Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800 mb-4\",\n                                                        children: \"Access all these models through a single API with intelligent routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors\",\n                                                        children: \"Start Free Trial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Choosing the right AI model depends on your specific use case, budget, and performance requirements. The key is to match the model's strengths to your needs while considering cost implications.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"With RouKey's intelligent routing, you don't have to choose just one model. Our system automatically selects the best model for each task, giving you the benefits of all models while optimizing for cost and performance.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-8 text-center\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/ai-api-gateway-2025-guide\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"The Complete Guide to AI API Gateways in 2025\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Discover how AI API gateways are revolutionizing multi-model routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/cost-effective-ai-development\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"Cost-Effective AI Development: Build AI Apps on a Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Practical strategies to reduce AI development costs by 70% using smart resource management.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = AIModelSelectionGuide;\nvar _c;\n$RefreshReg$(_c, \"AIModelSelectionGuide\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx\n"));

/***/ })

});