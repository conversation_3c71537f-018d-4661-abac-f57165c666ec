"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/page.tsx":
/*!*******************************!*\
  !*** ./src/app/blog/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Blog post data with SEO-optimized content\nconst blogPosts = [\n    {\n        id: 'ai-api-gateway-2025-guide',\n        title: 'The Complete Guide to AI API Gateways in 2025: Multi-Model Routing & Cost Optimization',\n        excerpt: 'Discover how AI API gateways are revolutionizing multi-model routing, reducing costs by 60%, and enabling seamless integration with 300+ AI models. Learn the latest strategies for 2025.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-15',\n        readTime: '8 min read',\n        category: 'AI Technology',\n        tags: [\n            'AI API Gateway',\n            'Multi-Model Routing',\n            'Cost Optimization',\n            'AI Integration',\n            'API Management'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-4636190af475?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: true,\n        seoKeywords: [\n            'AI API gateway 2025',\n            'multi-model routing',\n            'AI cost optimization',\n            'API management tools'\n        ]\n    },\n    {\n        id: 'bootstrap-lean-startup-2025',\n        title: 'How to Bootstrap a Lean Startup in 2025: From $0 to Revenue Without Funding',\n        excerpt: 'Learn the proven strategies to build and scale a lean startup without external funding. Real case studies, actionable frameworks, and growth hacks that work in 2025.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-12',\n        readTime: '12 min read',\n        category: 'Entrepreneurship',\n        tags: [\n            'Bootstrap Startup',\n            'Lean Methodology',\n            'No-Code Tools',\n            'MVP Development',\n            'Revenue Generation'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-47ba0277781c?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: true,\n        seoKeywords: [\n            'bootstrap startup 2025',\n            'lean startup methodology',\n            'startup without funding',\n            'MVP development'\n        ]\n    },\n    {\n        id: 'roukey-ai-routing-strategies',\n        title: 'RouKey\\'s Intelligent AI Routing: How We Achieved 99.9% Uptime with Multi-Provider Fallbacks',\n        excerpt: 'Behind the scenes of RouKey\\'s intelligent routing system. Learn how we built fault-tolerant AI infrastructure that automatically routes to the best-performing models.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-10',\n        readTime: '10 min read',\n        category: 'Product Deep Dive',\n        tags: [\n            'RouKey',\n            'AI Routing',\n            'Fault Tolerance',\n            'Infrastructure',\n            'Reliability'\n        ],\n        image: '/blog/roukey-routing.jpg',\n        featured: false,\n        seoKeywords: [\n            'AI routing strategies',\n            'multi-provider AI',\n            'AI infrastructure',\n            'fault tolerant AI'\n        ]\n    },\n    {\n        id: 'ai-model-selection-guide',\n        title: 'AI Model Selection Guide 2025: GPT-4, Claude, Gemini, and 300+ Models Compared',\n        excerpt: 'Comprehensive comparison of leading AI models in 2025. Performance benchmarks, cost analysis, and use-case recommendations for developers and businesses.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-08',\n        readTime: '15 min read',\n        category: 'AI Comparison',\n        tags: [\n            'AI Models 2025',\n            'GPT-4',\n            'Claude',\n            'Gemini',\n            'Model Comparison',\n            'AI Benchmarks'\n        ],\n        image: '/blog/ai-models-comparison.jpg',\n        featured: false,\n        seoKeywords: [\n            'AI model comparison 2025',\n            'best AI models',\n            'GPT-4 vs Claude',\n            'AI model selection'\n        ]\n    },\n    {\n        id: 'build-ai-powered-saas',\n        title: 'Building AI-Powered SaaS: Technical Architecture and Best Practices for 2025',\n        excerpt: 'Step-by-step guide to building scalable AI-powered SaaS applications. Architecture patterns, technology stack recommendations, and real-world implementation strategies.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-05',\n        readTime: '18 min read',\n        category: 'Technical Guide',\n        tags: [\n            'AI SaaS',\n            'Software Architecture',\n            'Scalability',\n            'Best Practices',\n            'Technical Implementation'\n        ],\n        image: '/blog/ai-saas-architecture.jpg',\n        featured: false,\n        seoKeywords: [\n            'AI-powered SaaS',\n            'AI software architecture',\n            'scalable AI applications',\n            'SaaS best practices'\n        ]\n    },\n    {\n        id: 'cost-effective-ai-development',\n        title: 'Cost-Effective AI Development: How to Build AI Apps on a Budget in 2025',\n        excerpt: 'Practical strategies to reduce AI development costs by 70%. Free tools, open-source alternatives, and smart resource management for indie developers and startups.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-03',\n        readTime: '14 min read',\n        category: 'Cost Optimization',\n        tags: [\n            'Cost-Effective AI',\n            'Budget Development',\n            'Open Source AI',\n            'Resource Management',\n            'Startup Tips'\n        ],\n        image: '/blog/cost-effective-ai.jpg',\n        featured: false,\n        seoKeywords: [\n            'cost-effective AI development',\n            'budget AI projects',\n            'cheap AI tools',\n            'AI development costs'\n        ]\n    }\n];\nconst categories = [\n    'All',\n    'AI Technology',\n    'Entrepreneurship',\n    'Product Deep Dive',\n    'AI Comparison',\n    'Technical Guide',\n    'Cost Optimization'\n];\nfunction BlogPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const filteredPosts = blogPosts.filter((post)=>{\n        const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;\n        const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) || post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) || post.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        return matchesCategory && matchesSearch;\n    });\n    const featuredPosts = blogPosts.filter((post)=>post.featured);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '60px 60px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6\n                                            },\n                                            className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                            children: [\n                                                \"RouKey \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#ff6b35]\",\n                                                    children: \"Blog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 24\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.1\n                                            },\n                                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                            children: \"Insights on AI technology, lean startup methodologies, and building cost-effective solutions. Learn from real-world experiences and industry best practices.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            className: \"max-w-md mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search articles...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    featuredPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-12 text-center\",\n                                    children: \"Featured Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: featuredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.article, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl transition-all duration-300 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video bg-gradient-to-br from-[#ff6b35] to-[#f7931e] relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-black/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-white/90 text-[#ff6b35] px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: post.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors\",\n                                                            children: post.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6 line-clamp-3\",\n                                                            children: post.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm text-gray-500 mb-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            post.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 211,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            new Date(post.date).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 215,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            post.readTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/blog/\".concat(post.id),\n                                                            className: \"inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors\",\n                                                            children: [\n                                                                \"Read More\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, post.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-8 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"px-6 py-2 rounded-full font-medium transition-all duration-200 \".concat(selectedCategory === category ? 'bg-[#ff6b35] text-white shadow-lg' : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-12 text-center\",\n                                    children: selectedCategory === 'All' ? 'All Articles' : \"\".concat(selectedCategory, \" Articles\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: filteredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.article, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.05\n                                            },\n                                            className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video bg-gradient-to-br from-[#ff6b35] to-[#f7931e] relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-black/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-white/90 text-[#ff6b35] px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: post.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors line-clamp-2\",\n                                                            children: post.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-3 text-sm\",\n                                                            children: post.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs text-gray-500 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        new Date(post.date).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        post.readTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-4\",\n                                                            children: post.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs\",\n                                                                    children: tag\n                                                                }, tag, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/blog/\".concat(post.id),\n                                                            className: \"inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors text-sm\",\n                                                            children: [\n                                                                \"Read More\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3 w-3 ml-1 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, post.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                filteredPosts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"No articles found matching your criteria.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPage, \"pI01Q/l1cGXnhBU+IE0+8yyKvu8=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/page.tsx\n"));

/***/ })

});