'use client';

import { motion } from 'framer-motion';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';

export default function Security() {
  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Security & Trust
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Learn how <PERSON><PERSON><PERSON><PERSON> protects your data, API keys, and AI requests with enterprise-grade security.
              </p>
              <div className="bg-green-50 border-l-4 border-green-500 p-6 rounded-r-lg">
                <p className="text-green-800">
                  <strong>Security First:</strong> Your data security and privacy are our top priorities. 
                  We implement industry-leading security measures to protect your information.
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Content */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              <div className="space-y-8 text-gray-800">
                
                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🔒 Data Protection</h2>
                  
                  <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg mb-6">
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">Zero Content Storage</h3>
                    <p className="text-blue-800">
                      RouKey does not store, log, or access the content of your AI requests and responses. 
                      Your conversations and data remain completely private.
                    </p>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Encryption Standards</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li><strong>In Transit:</strong> All data is encrypted using TLS 1.3 with perfect forward secrecy</li>
                    <li><strong>At Rest:</strong> Sensitive data is encrypted using AES-256 encryption</li>
                    <li><strong>API Keys:</strong> Stored using industry-standard encryption with unique salt values</li>
                    <li><strong>Database:</strong> All database connections use encrypted channels</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 mt-6">Data Minimization</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>We only collect data necessary for service operation</li>
                    <li>Personal information is limited to account essentials</li>
                    <li>Usage analytics are aggregated and anonymized</li>
                    <li>Automatic data purging for inactive accounts</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🛡️ Infrastructure Security</h2>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Cloud Security</h3>
                  <div className="grid md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">Vercel Platform</h4>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>SOC 2 Type II certified</li>
                        <li>Global edge network</li>
                        <li>DDoS protection</li>
                        <li>Automatic security updates</li>
                      </ul>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">Supabase Database</h4>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>PostgreSQL with RLS policies</li>
                        <li>Encrypted backups</li>
                        <li>Network isolation</li>
                        <li>Regular security patches</li>
                      </ul>
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Network Security</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Web Application Firewall (WAF) protection</li>
                    <li>Rate limiting and abuse prevention</li>
                    <li>IP allowlisting for enterprise customers</li>
                    <li>Continuous monitoring for threats</li>
                    <li>Automated incident response</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🔑 API Key Security</h2>
                  
                  <div className="bg-yellow-50 border-l-4 border-yellow-500 p-6 rounded-r-lg mb-6">
                    <h3 className="text-lg font-semibold text-yellow-900 mb-2">⚠️ Your Keys, Your Control</h3>
                    <p className="text-yellow-800">
                      You maintain full control over your AI provider API keys. RouKey encrypts and securely 
                      stores them, but you can revoke or rotate them at any time.
                    </p>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Key Management</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li><strong>Encryption:</strong> All API keys are encrypted with unique encryption keys</li>
                    <li><strong>Access Control:</strong> Keys are only accessible to your account</li>
                    <li><strong>Audit Logging:</strong> All key usage is logged for security monitoring</li>
                    <li><strong>Rotation Support:</strong> Easy key rotation without service interruption</li>
                    <li><strong>Secure Deletion:</strong> Keys are securely wiped when deleted</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 mt-6">Best Practices</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Use API keys with minimal required permissions</li>
                    <li>Regularly rotate your API keys</li>
                    <li>Monitor usage patterns for anomalies</li>
                    <li>Never share your RouKey account credentials</li>
                    <li>Enable two-factor authentication when available</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🔐 Authentication & Access Control</h2>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">User Authentication</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Secure email-based authentication</li>
                    <li>JWT tokens with short expiration times</li>
                    <li>Session management with automatic timeout</li>
                    <li>Password strength requirements</li>
                    <li>Account lockout protection against brute force attacks</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 mt-6">API Security</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>X-API-Key header authentication</li>
                    <li>Request signing and validation</li>
                    <li>Rate limiting per API key</li>
                    <li>IP-based access restrictions (Enterprise)</li>
                    <li>Real-time abuse detection</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">📊 Security Monitoring</h2>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Continuous Monitoring</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>24/7 security monitoring and alerting</li>
                    <li>Automated threat detection and response</li>
                    <li>Regular vulnerability assessments</li>
                    <li>Penetration testing by third-party security firms</li>
                    <li>Security incident response procedures</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 mt-6">Audit Logging</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Comprehensive audit trails for all actions</li>
                    <li>Immutable log storage</li>
                    <li>Real-time anomaly detection</li>
                    <li>Compliance reporting capabilities</li>
                    <li>Log retention according to industry standards</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🏆 Compliance & Certifications</h2>
                  
                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-green-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold text-green-900 mb-3">Privacy Compliance</h3>
                      <ul className="text-green-800 space-y-2">
                        <li>✅ GDPR compliant</li>
                        <li>✅ CCPA compliant</li>
                        <li>✅ Privacy by design</li>
                        <li>✅ Data minimization</li>
                      </ul>
                    </div>
                    <div className="bg-blue-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold text-blue-900 mb-3">Security Standards</h3>
                      <ul className="text-blue-800 space-y-2">
                        <li>🔒 SOC 2 Type II (via providers)</li>
                        <li>🔒 ISO 27001 aligned</li>
                        <li>🔒 OWASP Top 10 protection</li>
                        <li>🔒 Industry best practices</li>
                      </ul>
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Enterprise Features</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Single Sign-On (SSO) integration</li>
                    <li>Advanced audit logging and reporting</li>
                    <li>Custom data retention policies</li>
                    <li>Dedicated security support</li>
                    <li>On-premise deployment options (coming soon)</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🚨 Incident Response</h2>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Response Procedures</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Immediate containment and assessment</li>
                    <li>Rapid notification to affected users</li>
                    <li>Transparent communication about incidents</li>
                    <li>Post-incident analysis and improvements</li>
                    <li>Coordination with law enforcement if required</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 mt-6">Communication Channels</h3>
                  <div className="bg-red-50 border-l-4 border-red-500 p-6 rounded-r-lg">
                    <p className="text-red-800 mb-4">
                      In case of a security incident, we will notify affected users within 72 hours through:
                    </p>
                    <ul className="list-disc pl-6 space-y-1 text-red-800">
                      <li>Email notifications to registered addresses</li>
                      <li>In-app security alerts</li>
                      <li>Public status page updates</li>
                      <li>Direct communication for enterprise customers</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">🔍 Security Transparency</h2>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Regular Updates</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Quarterly security reports</li>
                    <li>Annual third-party security audits</li>
                    <li>Public disclosure of resolved vulnerabilities</li>
                    <li>Security roadmap and improvements</li>
                  </ul>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 mt-6">Bug Bounty Program</h3>
                  <div className="bg-purple-50 border-l-4 border-purple-500 p-6 rounded-r-lg">
                    <p className="text-purple-800 mb-4">
                      We welcome security researchers to help us maintain the highest security standards. 
                      Our responsible disclosure program includes:
                    </p>
                    <ul className="list-disc pl-6 space-y-1 text-purple-800">
                      <li>Clear reporting guidelines</li>
                      <li>Rapid response to valid reports</li>
                      <li>Recognition for security researchers</li>
                      <li>Coordinated disclosure process</li>
                    </ul>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">📞 Security Contact</h2>
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <p className="mb-4">
                      For security-related inquiries, vulnerability reports, or urgent security matters:
                    </p>
                    <ul className="space-y-2">
                      <li><strong>Security Email:</strong> <a href="mailto:<EMAIL>" className="text-[#ff6b35] hover:underline"><EMAIL></a></li>
                      <li><strong>General Contact:</strong> <a href="mailto:<EMAIL>" className="text-[#ff6b35] hover:underline"><EMAIL></a></li>
                      <li><strong>Emergency:</strong> <a href="mailto:<EMAIL>" className="text-[#ff6b35] hover:underline"><EMAIL></a></li>
                    </ul>
                    <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        <strong>PGP Key:</strong> For sensitive security communications, please use our PGP key 
                        available at <a href="/pgp-key" className="text-[#ff6b35] hover:underline">roukey.online/pgp-key</a>
                      </p>
                    </div>
                    <p className="mt-4 text-sm text-gray-600">
                      Security reports will be acknowledged within 24 hours and addressed according to severity.
                    </p>
                  </div>
                </section>

              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
