'use client';

import { motion } from 'framer-motion';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';

export default function CookiePolicy() {
  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-8">
                Cookie Policy
              </h1>
              <p className="text-2xl text-gray-600 mb-10 max-w-4xl">
                Learn how R<PERSON><PERSON>ey uses cookies and similar technologies to enhance your experience.
              </p>
              <div className="bg-blue-50 border-l-4 border-blue-500 p-8 rounded-r-lg max-w-4xl">
                <p className="text-blue-800 text-lg">
                  <strong>Last Updated:</strong> June 25, 2025<br/>
                  <strong>Effective Date:</strong> June 25, 2025
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Content */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-xl max-w-none"
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-12 text-gray-800">
                
                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">1. What Are Cookies?</h2>
                  <p className="mb-4">
                    Cookies are small text files that are stored on your device when you visit a website. 
                    They help websites remember your preferences, improve functionality, and provide analytics 
                    about how the site is used.
                  </p>
                  <p className="mb-4">
                    RouKey uses cookies and similar technologies to enhance your experience, maintain security, 
                    and improve our services.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">2. Types of Cookies We Use</h2>
                  
                  <div className="space-y-6">
                    <div className="bg-green-50 border-l-4 border-green-500 p-6 rounded-r-lg">
                      <h3 className="text-xl font-semibold text-green-900 mb-3">🔒 Essential Cookies</h3>
                      <p className="text-green-800 mb-3">
                        These cookies are necessary for the website to function properly and cannot be disabled.
                      </p>
                      <ul className="list-disc pl-6 space-y-1 text-green-800">
                        <li>Authentication and session management</li>
                        <li>Security and fraud prevention</li>
                        <li>Load balancing and performance</li>
                        <li>CSRF protection tokens</li>
                      </ul>
                    </div>

                    <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg">
                      <h3 className="text-xl font-semibold text-blue-900 mb-3">⚙️ Functional Cookies</h3>
                      <p className="text-blue-800 mb-3">
                        These cookies enable enhanced functionality and personalization.
                      </p>
                      <ul className="list-disc pl-6 space-y-1 text-blue-800">
                        <li>User preferences and settings</li>
                        <li>Language and region selection</li>
                        <li>Dashboard customization</li>
                        <li>API configuration storage</li>
                      </ul>
                    </div>

                    <div className="bg-purple-50 border-l-4 border-purple-500 p-6 rounded-r-lg">
                      <h3 className="text-xl font-semibold text-purple-900 mb-3">📊 Analytics Cookies</h3>
                      <p className="text-purple-800 mb-3">
                        These cookies help us understand how you use our service to improve it.
                      </p>
                      <ul className="list-disc pl-6 space-y-1 text-purple-800">
                        <li>Usage patterns and feature adoption</li>
                        <li>Performance monitoring</li>
                        <li>Error tracking and debugging</li>
                        <li>A/B testing for improvements</li>
                      </ul>
                    </div>

                    <div className="bg-orange-50 border-l-4 border-orange-500 p-6 rounded-r-lg">
                      <h3 className="text-xl font-semibold text-orange-900 mb-3">🎯 Performance Cookies</h3>
                      <p className="text-orange-800 mb-3">
                        These cookies optimize the performance and speed of our service.
                      </p>
                      <ul className="list-disc pl-6 space-y-1 text-orange-800">
                        <li>Caching and content delivery</li>
                        <li>Load time optimization</li>
                        <li>Resource compression</li>
                        <li>Network routing efficiency</li>
                      </ul>
                    </div>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">3. Specific Cookies Used</h2>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cookie Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purpose</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">auth-token</td>
                          <td className="px-6 py-4 text-sm text-gray-500">User authentication</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Session</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Essential</td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">csrf-token</td>
                          <td className="px-6 py-4 text-sm text-gray-500">Security protection</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Session</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Essential</td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">user-preferences</td>
                          <td className="px-6 py-4 text-sm text-gray-500">Dashboard settings</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 year</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Functional</td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">analytics-id</td>
                          <td className="px-6 py-4 text-sm text-gray-500">Usage analytics</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 years</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Analytics</td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">performance-cache</td>
                          <td className="px-6 py-4 text-sm text-gray-500">Page load optimization</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">24 hours</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Performance</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">4. Third-Party Cookies</h2>
                  <p className="mb-4">
                    RouKey may use third-party services that set their own cookies. These services include:
                  </p>
                  
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Stripe (Payment Processing)</h3>
                      <ul className="list-disc pl-6 space-y-1 text-gray-700">
                        <li>Fraud detection and prevention</li>
                        <li>Payment form functionality</li>
                        <li>Transaction security</li>
                      </ul>
                      <p className="text-sm text-gray-600 mt-2">
                        <a href="https://stripe.com/privacy" className="text-[#ff6b35] hover:underline" target="_blank" rel="noopener noreferrer">
                          View Stripe's Privacy Policy
                        </a>
                      </p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Supabase (Database & Auth)</h3>
                      <ul className="list-disc pl-6 space-y-1 text-gray-700">
                        <li>Authentication management</li>
                        <li>Session persistence</li>
                        <li>Database connections</li>
                      </ul>
                      <p className="text-sm text-gray-600 mt-2">
                        <a href="https://supabase.com/privacy" className="text-[#ff6b35] hover:underline" target="_blank" rel="noopener noreferrer">
                          View Supabase's Privacy Policy
                        </a>
                      </p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Vercel (Hosting & Analytics)</h3>
                      <ul className="list-disc pl-6 space-y-1 text-gray-700">
                        <li>Performance monitoring</li>
                        <li>Error tracking</li>
                        <li>Speed insights</li>
                      </ul>
                      <p className="text-sm text-gray-600 mt-2">
                        <a href="https://vercel.com/legal/privacy-policy" className="text-[#ff6b35] hover:underline" target="_blank" rel="noopener noreferrer">
                          View Vercel's Privacy Policy
                        </a>
                      </p>
                    </div>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Managing Your Cookie Preferences</h2>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Browser Settings</h3>
                  <p className="mb-4">
                    You can control cookies through your browser settings. Here's how to manage cookies in popular browsers:
                  </p>
                  
                  <div className="grid md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">Chrome</h4>
                      <p className="text-sm text-gray-700">Settings → Privacy and Security → Cookies and other site data</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">Firefox</h4>
                      <p className="text-sm text-gray-700">Settings → Privacy & Security → Cookies and Site Data</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">Safari</h4>
                      <p className="text-sm text-gray-700">Preferences → Privacy → Manage Website Data</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">Edge</h4>
                      <p className="text-sm text-gray-700">Settings → Cookies and site permissions → Cookies and site data</p>
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Cookie Preferences Center</h3>
                  <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg">
                    <p className="text-blue-800 mb-4">
                      You can manage your cookie preferences for RouKey through our Cookie Preferences Center, 
                      accessible from the footer of our website.
                    </p>
                    <button className="bg-[#ff6b35] text-white px-6 py-2 rounded-lg hover:bg-[#e55a2b] transition-colors">
                      Manage Cookie Preferences
                    </button>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Impact of Disabling Cookies</h2>
                  
                  <div className="bg-yellow-50 border-l-4 border-yellow-500 p-6 rounded-r-lg mb-6">
                    <h3 className="text-lg font-semibold text-yellow-900 mb-2">⚠️ Important Notice</h3>
                    <p className="text-yellow-800">
                      Disabling certain cookies may affect the functionality of RouKey. Essential cookies 
                      cannot be disabled as they are necessary for the service to work.
                    </p>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3">What Happens When You Disable:</h3>
                  <ul className="list-disc pl-6 space-y-2">
                    <li><strong>Functional Cookies:</strong> You may lose personalized settings and preferences</li>
                    <li><strong>Analytics Cookies:</strong> We cannot improve the service based on usage patterns</li>
                    <li><strong>Performance Cookies:</strong> The service may load slower or less efficiently</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">7. Mobile Apps and Local Storage</h2>
                  <p className="mb-4">
                    If you use RouKey through mobile applications, we may use local storage technologies 
                    similar to cookies, including:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Local storage and session storage</li>
                    <li>Application cache</li>
                    <li>IndexedDB for offline functionality</li>
                    <li>Device identifiers for analytics</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">8. Updates to This Policy</h2>
                  <p className="mb-4">
                    We may update this Cookie Policy periodically to reflect changes in our practices 
                    or applicable laws. We will notify you of significant changes by:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Email notification to registered users</li>
                    <li>Banner notification on our website</li>
                    <li>Updated "Last Modified" date at the top of this policy</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">9. Contact Us</h2>
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <p className="mb-4">
                      If you have questions about our use of cookies or this Cookie Policy, please contact us:
                    </p>
                    <ul className="space-y-2">
                      <li><strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-[#ff6b35] hover:underline"><EMAIL></a></li>
                      <li><strong>Privacy Officer:</strong> <a href="mailto:<EMAIL>" className="text-[#ff6b35] hover:underline"><EMAIL></a></li>
                      <li><strong>Subject Line:</strong> "Cookie Policy Inquiry"</li>
                    </ul>
                    <p className="mt-4 text-sm text-gray-600">
                      We will respond to your inquiry within 5 business days.
                    </p>
                  </div>
                </section>

                </div>

                {/* Sidebar */}
                <div className="lg:col-span-1">
                  <div className="sticky top-24 space-y-8">
                    <div className="bg-gray-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Cookie Types</h3>
                      <nav className="space-y-2">
                        <a href="#essential" className="block text-[#ff6b35] hover:text-[#e55a2b] transition-colors">🔒 Essential</a>
                        <a href="#functional" className="block text-[#ff6b35] hover:text-[#e55a2b] transition-colors">⚙️ Functional</a>
                        <a href="#analytics" className="block text-[#ff6b35] hover:text-[#e55a2b] transition-colors">📊 Analytics</a>
                        <a href="#performance" className="block text-[#ff6b35] hover:text-[#e55a2b] transition-colors">🎯 Performance</a>
                      </nav>
                    </div>

                    <div className="bg-purple-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold text-purple-900 mb-3">🍪 Cookie Control</h3>
                      <p className="text-purple-800 text-sm mb-4">
                        Manage your cookie preferences
                      </p>
                      <button className="bg-[#ff6b35] text-white px-4 py-2 rounded-lg hover:bg-[#e55a2b] transition-colors text-sm">
                        Cookie Settings
                      </button>
                    </div>

                    <div className="bg-yellow-50 p-6 rounded-lg">
                      <h3 className="text-lg font-semibold text-yellow-900 mb-3">⚠️ Browser Settings</h3>
                      <ul className="space-y-2 text-yellow-800 text-sm">
                        <li>• Chrome: Settings → Privacy</li>
                        <li>• Firefox: Privacy & Security</li>
                        <li>• Safari: Preferences → Privacy</li>
                        <li>• Edge: Cookies & site data</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
