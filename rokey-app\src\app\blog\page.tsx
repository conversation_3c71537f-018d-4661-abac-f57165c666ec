'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { CalendarIcon, ClockIcon, UserIcon, TagIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import Landing<PERSON>avbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';

// Blog post data with SEO-optimized content
const blogPosts = [
  {
    id: 'ai-api-gateway-2025-guide',
    title: 'The Complete Guide to AI API Gateways in 2025: Multi-Model Routing & Cost Optimization',
    excerpt: 'Discover how AI API gateways are revolutionizing multi-model routing, reducing costs by 60%, and enabling seamless integration with 300+ AI models. Learn the latest strategies for 2025.',
    content: 'Full blog content here...',
    author: '<PERSON>',
    date: '2025-01-15',
    readTime: '8 min read',
    category: 'AI Technology',
    tags: ['AI API Gateway', 'Multi-Model Routing', 'Cost Optimization', 'AI Integration', 'API Management'],
    image: 'https://images.unsplash.com/photo-*************-4636190af475?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',
    featured: true,
    seoKeywords: ['AI API gateway 2025', 'multi-model routing', 'AI cost optimization', 'API management tools']
  },
  {
    id: 'bootstrap-lean-startup-2025',
    title: 'How to Bootstrap a Lean Startup in 2025: From $0 to Revenue Without Funding',
    excerpt: 'Learn the proven strategies to build and scale a lean startup without external funding. Real case studies, actionable frameworks, and growth hacks that work in 2025.',
    content: 'Full blog content here...',
    author: 'David Okoro',
    date: '2025-01-12',
    readTime: '12 min read',
    category: 'Entrepreneurship',
    tags: ['Bootstrap Startup', 'Lean Methodology', 'No-Code Tools', 'MVP Development', 'Revenue Generation'],
    image: 'https://images.unsplash.com/photo-*************-47ba0277781c?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',
    featured: true,
    seoKeywords: ['bootstrap startup 2025', 'lean startup methodology', 'startup without funding', 'MVP development']
  },
  {
    id: 'roukey-ai-routing-strategies',
    title: 'RouKey\'s Intelligent AI Routing: How We Achieved 99.9% Uptime with Multi-Provider Fallbacks',
    excerpt: 'Behind the scenes of RouKey\'s intelligent routing system. Learn how we built fault-tolerant AI infrastructure that automatically routes to the best-performing models.',
    content: 'Full blog content here...',
    author: 'David Okoro',
    date: '2025-01-10',
    readTime: '10 min read',
    category: 'Product Deep Dive',
    tags: ['RouKey', 'AI Routing', 'Fault Tolerance', 'Infrastructure', 'Reliability'],
    image: 'https://plus.unsplash.com/premium_photo-*************-252eab5fd2db?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',
    featured: false,
    seoKeywords: ['AI routing strategies', 'multi-provider AI', 'AI infrastructure', 'fault tolerant AI']
  },
  {
    id: 'ai-model-selection-guide',
    title: 'AI Model Selection Guide 2025: GPT-4, Claude, Gemini, and 300+ Models Compared',
    excerpt: 'Comprehensive comparison of leading AI models in 2025. Performance benchmarks, cost analysis, and use-case recommendations for developers and businesses.',
    content: 'Full blog content here...',
    author: 'David Okoro',
    date: '2025-01-08',
    readTime: '15 min read',
    category: 'AI Comparison',
    tags: ['AI Models 2025', 'GPT-4', 'Claude', 'Gemini', 'Model Comparison', 'AI Benchmarks'],
    image: 'https://images.unsplash.com/photo-*************-89b55fcc595e?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',
    featured: false,
    seoKeywords: ['AI model comparison 2025', 'best AI models', 'GPT-4 vs Claude', 'AI model selection']
  },
  {
    id: 'build-ai-powered-saas',
    title: 'Building AI-Powered SaaS: Technical Architecture and Best Practices for 2025',
    excerpt: 'Step-by-step guide to building scalable AI-powered SaaS applications. Architecture patterns, technology stack recommendations, and real-world implementation strategies.',
    content: 'Full blog content here...',
    author: 'David Okoro',
    date: '2025-01-05',
    readTime: '18 min read',
    category: 'Technical Guide',
    tags: ['AI SaaS', 'Software Architecture', 'Scalability', 'Best Practices', 'Technical Implementation'],
    image: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',
    featured: false,
    seoKeywords: ['AI-powered SaaS', 'AI software architecture', 'scalable AI applications', 'SaaS best practices']
  },
  {
    id: 'cost-effective-ai-development',
    title: 'Cost-Effective AI Development: How to Build AI Apps on a Budget in 2025',
    excerpt: 'Practical strategies to reduce AI development costs by 70%. Free tools, open-source alternatives, and smart resource management for indie developers and startups.',
    content: 'Full blog content here...',
    author: 'David Okoro',
    date: '2025-01-03',
    readTime: '14 min read',
    category: 'Cost Optimization',
    tags: ['Cost-Effective AI', 'Budget Development', 'Open Source AI', 'Resource Management', 'Startup Tips'],
    image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',
    featured: false,
    seoKeywords: ['cost-effective AI development', 'budget AI projects', 'cheap AI tools', 'AI development costs']
  }
];

const categories = ['All', 'AI Technology', 'Entrepreneurship', 'Product Deep Dive', 'AI Comparison', 'Technical Guide', 'Cost Optimization'];

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const featuredPosts = blogPosts.filter(post => post.featured);

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
          <div className="absolute inset-0 opacity-5">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '60px 60px'
              }}
            ></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="text-center">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-4xl md:text-6xl font-bold text-gray-900 mb-6"
              >
                RouKey <span className="text-[#ff6b35]">Blog</span>
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto"
              >
                Insights on AI technology, lean startup methodologies, and building cost-effective solutions. 
                Learn from real-world experiences and industry best practices.
              </motion.p>

              {/* Search Bar */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="max-w-md mx-auto"
              >
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
                />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Featured Articles</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {featuredPosts.map((post, index) => (
                  <motion.article
                    key={post.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl transition-all duration-300 group"
                  >
                    <div className="aspect-video bg-gradient-to-br from-[#ff6b35] to-[#f7931e] relative">
                      <div className="absolute inset-0 bg-black/20"></div>
                      <div className="absolute bottom-4 left-4">
                        <span className="bg-white/90 text-[#ff6b35] px-3 py-1 rounded-full text-sm font-medium">
                          {post.category}
                        </span>
                      </div>
                    </div>
                    <div className="p-8">
                      <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 mb-6 line-clamp-3">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 mr-1" />
                            {post.author}
                          </div>
                          <div className="flex items-center">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            {new Date(post.date).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            {post.readTime}
                          </div>
                        </div>
                      </div>
                      <Link
                        href={`/blog/${post.id}`}
                        className="inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors"
                      >
                        Read More
                        <ArrowRightIcon className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </motion.article>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Category Filter */}
        <section className="py-8 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-wrap justify-center gap-4">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-2 rounded-full font-medium transition-all duration-200 ${
                    selectedCategory === category
                      ? 'bg-[#ff6b35] text-white shadow-lg'
                      : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* All Posts */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
              {selectedCategory === 'All' ? 'All Articles' : `${selectedCategory} Articles`}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.05 }}
                  className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group"
                >
                  <div className="aspect-video bg-gradient-to-br from-[#ff6b35] to-[#f7931e] relative">
                    <div className="absolute inset-0 bg-black/20"></div>
                    <div className="absolute bottom-3 left-3">
                      <span className="bg-white/90 text-[#ff6b35] px-2 py-1 rounded-full text-xs font-medium">
                        {post.category}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3 text-sm">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                      <div className="flex items-center">
                        <CalendarIcon className="h-3 w-3 mr-1" />
                        {new Date(post.date).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                    <Link
                      href={`/blog/${post.id}`}
                      className="inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors text-sm"
                    >
                      Read More
                      <ArrowRightIcon className="h-3 w-3 ml-1 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </motion.article>
              ))}
            </div>

            {filteredPosts.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No articles found matching your criteria.</p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
