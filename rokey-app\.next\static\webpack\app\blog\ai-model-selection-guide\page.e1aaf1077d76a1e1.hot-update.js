"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/ai-model-selection-guide/page",{

/***/ "(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/blog/ai-model-selection-guide/page.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIModelSelectionGuide)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction AIModelSelectionGuide() {\n    const post = {\n        title: 'Best AI Models 2025: OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro & DeepSeek R1 Compared',\n        author: 'David Okoro',\n        date: '2025-06-25',\n        readTime: '18 min read',\n        category: 'AI Comparison',\n        tags: [\n            'Best AI Models 2025',\n            'OpenAI o3',\n            'Claude 4 Opus',\n            'Gemini 2.5 Pro',\n            'DeepSeek R1',\n            'AI Benchmarks',\n            'Model Performance'\n        ],\n        excerpt: 'Comprehensive comparison of the latest AI models in 2025. Performance benchmarks, cost analysis, coding capabilities, reasoning tests, and multimodal features across OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"AI Model Selection - White robot representing artificial intelligence\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"AI Model Comparison 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape in 2025 has reached unprecedented sophistication. With breakthrough models like OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1 leading the charge, we're witnessing capabilities that seemed impossible just months ago. This comprehensive guide analyzes the latest performance benchmarks, cost structures, and specialized use cases to help you choose the perfect AI model for your needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF What You'll Learn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: [\n                                                            \"• Performance benchmarks across GPQA Diamond, AIME 2024, SWE Bench, and BFCL tests\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 103\n                                                            }, this),\n                                                            \"• Cost analysis per million tokens for input/output\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 72\n                                                            }, this),\n                                                            \"• Specialized capabilities: coding, reasoning, multimodal, and speed\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 89\n                                                            }, this),\n                                                            \"• Real-world use case recommendations for different business needs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFC6 2025 AI Model Champions by Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Reasoning Champion: Gemini 2.5 Pro\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Complex reasoning, mathematical problems, scientific analysis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 86.4% GPQA Diamond (highest reasoning score)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Unmatched performance on complex reasoning tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Exceptional mathematical and scientific problem-solving\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong multimodal capabilities with vision and audio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent context understanding and logical deduction\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $1.25/1M input tokens, $5.00/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Coding Champion: Claude 4 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Software development, code review, debugging, technical documentation\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 72.7% SWE Bench (highest coding score)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Superior code generation across all programming languages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent debugging and code optimization capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong architectural decision-making and best practices\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Exceptional at explaining complex technical concepts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $3.00/1M input tokens, $15.00/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Speed Champion: Llama 4 Scout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Real-time applications, high-throughput processing, latency-sensitive tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 2,600 tokens/second (fastest response time)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Blazing-fast response times for real-time applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent for chatbots and interactive applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good balance of speed and quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Optimized for high-volume concurrent requests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.20/1M input tokens, $0.80/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Cost Champion: Nova Micro\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Budget-conscious applications, high-volume processing, simple tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.04/$0.14 per 1M tokens (most cost-effective)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Extremely cost-effective for large-scale deployments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good performance for simple to medium complexity tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Reliable and consistent output quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Perfect for content generation and basic analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.04/1M input tokens, $0.14/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border-l-4 border-green-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-green-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCA1 RouKey's Smart Advantage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-800\",\n                                                        children: \"Why choose one model when you can have them all? RouKey's intelligent routing automatically selects the best model for each task - Gemini 2.5 Pro for complex reasoning, Claude 4 Sonnet for coding, Llama 4 Scout for speed, and Nova Micro for cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFAF Specialized Use Cases & Model Rankings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83D\\uDCBB Best Models for Coding & Development\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Claude 4 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 72.7% SWE Bench - Best overall for code generation, debugging, and architecture\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. DeepSeek R1:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 71.9% SWE Bench - Excellent for complex algorithms and system design\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 71.7% SWE Bench - Strong at code explanation and refactoring\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. Claude 3.7 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 69.2% SWE Bench - Great for code reviews and documentation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDDE0 Best Models for Complex Reasoning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Gemini 2.5 Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 86.4% GPQA Diamond - Unmatched scientific and mathematical reasoning\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 85.5% GPQA Diamond - Excellent logical deduction and problem-solving\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. Claude 4 Opus:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 84.9% GPQA Diamond - Strong analytical thinking and research\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. DeepSeek R1:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 84.1% GPQA Diamond - Great for technical analysis and planning\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83C\\uDFA8 Best Models for Creative & Content Writing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Claude 4 Opus:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Superior creative storytelling and narrative development\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent for marketing copy and persuasive writing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. Gemini 2.5 Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Great for technical writing and documentation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. Claude 3.7 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Strong analytical and research-based content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83D\\uDDBC️ Best Models for Multimodal Tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Gemini 2.5 Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Advanced vision, audio, and video processing capabilities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. Claude 4 Opus:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent image analysis and visual reasoning\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Strong multimodal understanding and generation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. Llama 4 Vision:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Fast multimodal processing for real-time applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Cost-Performance Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Model\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Input Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Output Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"GPT-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$30/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$60/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Excellent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Claude 3.5 Sonnet\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$3/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$15/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 243,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Excellent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 244,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Gemini 2.0 Flash\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.075/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.30/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Very Good\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Use Case Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"For Startups and Small Businesses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Gemini 2.0 Flash for cost-effective general tasks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Claude 3.5 Sonnet for complex analysis and code\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Fallback:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" GPT-4 for tasks requiring highest quality\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"For Enterprise Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Claude 3.5 Sonnet for reliability and safety\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" GPT-4 for creative and complex reasoning tasks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"High-volume:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Gemini 2.0 Flash for cost optimization\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Future Trends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape continues to evolve rapidly. Key trends to watch:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Specialized Models:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" More domain-specific models for niche use cases\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Cost Reduction:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Continued price competition driving costs down\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Multimodal Capabilities:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Better integration of text, image, and audio\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Edge Deployment:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Smaller models optimized for local deployment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83D\\uDE80 Get Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800 mb-4\",\n                                                        children: \"Access all these models through a single API with intelligent routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors\",\n                                                        children: \"Start Free Trial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Choosing the right AI model depends on your specific use case, budget, and performance requirements. The key is to match the model's strengths to your needs while considering cost implications.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"With RouKey's intelligent routing, you don't have to choose just one model. Our system automatically selects the best model for each task, giving you the benefits of all models while optimizing for cost and performance.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-8 text-center\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/ai-api-gateway-2025-guide\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"The Complete Guide to AI API Gateways in 2025\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Discover how AI API gateways are revolutionizing multi-model routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/cost-effective-ai-development\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"Cost-Effective AI Development: Build AI Apps on a Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Practical strategies to reduce AI development costs by 70% using smart resource management.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = AIModelSelectionGuide;\nvar _c;\n$RefreshReg$(_c, \"AIModelSelectionGuide\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx\n"));

/***/ })

});