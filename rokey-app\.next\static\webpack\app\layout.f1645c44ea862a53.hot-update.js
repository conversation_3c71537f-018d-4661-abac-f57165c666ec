"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"43c2b84c9f85\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQzYzJiODRjOWY4NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ConditionalLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/ConditionalLayout.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConditionalLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_LayoutContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LayoutContent */ \"(app-pages-browser)/./src/components/LayoutContent.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ConditionalLayout(param) {\n    let { children } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { toasts, removeToast } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Check if this is a marketing page (landing, pricing, auth, features, about, routing-strategies, contact, docs, blog, legal pages)\n    const isMarketingPage = pathname === '/' || pathname.startsWith('/pricing') || pathname.startsWith('/features') || pathname.startsWith('/about') || pathname.startsWith('/routing-strategies') || pathname.startsWith('/contact') || pathname.startsWith('/docs') || pathname.startsWith('/blog') || pathname.startsWith('/auth/') || pathname.startsWith('/privacy') || pathname.startsWith('/terms') || pathname.startsWith('/cookies') || pathname.startsWith('/security');\n    // For marketing pages, render children directly without sidebar/navbar but with toast support\n    if (isMarketingPage) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.ToastContainer, {\n                    toasts: toasts,\n                    onRemove: removeToast\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // For app pages, use the full layout with sidebar and navbar\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_2__.SidebarProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.NavigationProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LayoutContent__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(ConditionalLayout, \"YLbiQmjv4qvNOyM2XZnsD9bvL9k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = ConditionalLayout;\nvar _c;\n$RefreshReg$(_c, \"ConditionalLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ConditionalLayout.tsx\n"));

/***/ })

});