"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/bootstrap-lean-startup-2025/page",{

/***/ "(app-pages-browser)/./src/app/blog/bootstrap-lean-startup-2025/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/blog/bootstrap-lean-startup-2025/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BootstrapLeanStartup2025)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction BootstrapLeanStartup2025() {\n    const post = {\n        title: 'How to Bootstrap a Lean Startup in 2025: From $0 to Revenue Without Funding',\n        author: 'David Okoro',\n        date: '2025-01-12',\n        readTime: '12 min read',\n        category: 'Entrepreneurship',\n        tags: [\n            'Bootstrap Startup',\n            'Lean Methodology',\n            'No-Code Tools',\n            'MVP Development',\n            'Revenue Generation'\n        ],\n        excerpt: 'Learn the proven strategies to build and scale a lean startup without external funding. Real case studies, actionable frameworks, and growth hacks that work in 2025.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1519389950473-47ba0277781c?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"Bootstrap Startup - Entrepreneurs working together on laptops\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"Bootstrap Your Way to Success\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"In 2025, the startup landscape has fundamentally changed. With the rise of no-code tools, AI-powered development, and global remote work, it's never been easier to build a profitable business from scratch without external funding. This comprehensive guide will show you exactly how to bootstrap a lean startup using proven methodologies and modern tools.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCCA Bootstrap Success Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: \"82% of successful startups are self-funded, and bootstrapped companies have a 93% higher survival rate than venture-backed startups in their first 5 years.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The Bootstrap Mindset: Think Different\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: 'Bootstrapping isn\\'t just about not taking funding—it\\'s a completely different approach to building a business. Instead of \"growth at all costs,\" you focus on \"profit from day one.\" This mindset shift changes everything about how you build, market, and scale your startup.'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Key Principles of Bootstrap Success:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Revenue First:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Focus on generating revenue from the earliest possible moment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Lean Operations:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Keep costs minimal and optimize for efficiency\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Customer-Driven Development:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Build only what customers will pay for\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Organic Growth:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Rely on word-of-mouth and content marketing over paid ads\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Reinvestment Strategy:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Plow profits back into growth rather than external funding\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Phase 1: Validate Before You Build (Weeks 1-4)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"1. Problem Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Before writing a single line of code, you need to identify a real problem that people are willing to pay to solve. Use these techniques:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Customer Interviews:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Talk to 50+ potential customers about their pain points\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Online Communities:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Monitor Reddit, Discord, and industry forums for recurring complaints\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Competitor Analysis:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Study what existing solutions are missing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Personal Experience:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Solve a problem you personally face\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"2. Market Validation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Validate demand before building anything substantial:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Landing Page Test:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Create a simple landing page and measure sign-ups\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Pre-Sales:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Sell the product before building it (offer refunds if needed)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Waitlist Building:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Build an email list of interested prospects\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Social Proof:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Share your idea on social media and gauge response\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border-l-4 border-green-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-green-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCA1 Validation Success Story\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-800\",\n                                                        children: \"RouKey was validated by pre-selling to 50 developers who signed up for early access. This validated demand and provided initial revenue before the product was fully built.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Phase 2: Build Your MVP (Weeks 5-12)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"The Modern No-Code/Low-Code Stack\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"In 2025, you can build sophisticated applications without extensive coding knowledge:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mt-6 mb-3\",\n                                                children: \"Frontend Development:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Next.js + Vercel:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For fast, scalable web applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Bubble:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" No-code platform for complex web apps\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Webflow:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For marketing sites and simple applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Framer:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" For interactive prototypes and landing pages\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mt-6 mb-3\",\n                                                children: \"Backend & Database:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Supabase:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Open-source Firebase alternative with PostgreSQL\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Airtable:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Spreadsheet-database hybrid for simple data needs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Firebase:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Google's backend-as-a-service platform\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"PlanetScale:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Serverless MySQL platform\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mt-6 mb-3\",\n                                                children: \"Payment Processing:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Stripe:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Industry standard for online payments\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Paddle:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Merchant of record for global sales\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Gumroad:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Simple digital product sales\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"MVP Development Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Your MVP should solve the core problem with minimal features:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Core Feature Only:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Build one feature that solves the main problem\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Manual Processes:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Use manual work where automation isn't critical\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Simple UI:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Focus on functionality over aesthetics initially\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Quick Iteration:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Plan for weekly releases and improvements\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Phase 3: Launch and Generate Revenue (Weeks 13-24)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Launch Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"A successful bootstrap launch focuses on organic growth and community building:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mt-6 mb-3\",\n                                                children: \"Content Marketing:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Blog Content:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Write about your industry and share insights\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Social Media:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Share your building journey on Twitter/LinkedIn\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"YouTube/Podcasts:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Create educational content in your niche\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Guest Writing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Contribute to industry publications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-gray-900 mt-6 mb-3\",\n                                                children: \"Community Engagement:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Product Hunt:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Launch on Product Hunt for visibility\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Reddit/Discord:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Engage in relevant communities (don't spam)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Industry Forums:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Participate in niche communities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Networking:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Connect with other entrepreneurs and potential customers\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Pricing Strategy for Bootstrappers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Pricing is crucial for bootstrap success. Here's what works:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Value-Based Pricing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Price based on value delivered, not costs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Tiered Pricing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Offer multiple tiers to capture different segments\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Annual Discounts:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Encourage annual payments for better cash flow\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Freemium Model:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Free tier to attract users, paid tiers for revenue\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCB0 Revenue Milestone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800\",\n                                                        children: \"Aim for $1,000 MRR (Monthly Recurring Revenue) within 6 months. This proves product-market fit and provides foundation for scaling.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Phase 4: Scale and Optimize (Months 6-12)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Growth Optimization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Once you have initial traction, focus on optimizing your growth engine:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Customer Success:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Ensure customers get value and reduce churn\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Referral Programs:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Incentivize existing customers to refer others\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Product Improvements:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Continuously improve based on user feedback\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"SEO Optimization:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Invest in long-term organic traffic growth\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Operational Efficiency\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"As you grow, optimize operations to maintain lean principles:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Automation:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Automate repetitive tasks and processes\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Outsourcing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Delegate non-core activities to freelancers\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Systems:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Implement systems for customer support, billing, etc.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Metrics:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Track key metrics and optimize based on data\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Common Bootstrap Mistakes to Avoid\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Building Too Much Too Soon:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Start with the smallest viable solution\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Ignoring Customer Feedback:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Listen to users and iterate quickly\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Perfectionism:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Ship early and improve based on real usage\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Underpricing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Don't compete on price; compete on value\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Scaling Too Fast:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Ensure unit economics work before scaling\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Neglecting Marketing:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Start marketing from day one, not after building\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The RouKey Bootstrap Story\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"RouKey itself is a bootstrap success story. Starting with $0 in funding, we:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Validated the idea through developer interviews and community feedback\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Built an MVP using modern tools like Next.js, Supabase, and Vercel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Launched with a freemium model to attract users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Focused on content marketing and community engagement\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Achieved profitability within 8 months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF Key Takeaway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: \"Bootstrap success isn't about having the perfect idea or unlimited time. It's about executing consistently, listening to customers, and optimizing for profitability from day one.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Your Bootstrap Action Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Ready to start your bootstrap journey? Here's your immediate action plan:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Week 1-2: Problem Discovery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Conduct 20 customer interviews\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Research existing solutions and their limitations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Define your unique value proposition\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Week 3-4: Validation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Create a landing page with email signup\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Share your idea in relevant communities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Aim for 100+ email signups\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Week 5-12: Build MVP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Choose your tech stack\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Build the core feature only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Set up payment processing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Launch to your email list\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Bootstrapping a startup in 2025 is more achievable than ever before. With the right mindset, modern tools, and proven methodologies, you can build a profitable business without external funding. The key is to start small, validate early, and focus on generating revenue from day one.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Remember: every successful bootstrap story started with someone taking the first step. Your journey begins with identifying a real problem and committing to solving it profitably. The tools and strategies are available—now it's time to execute.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83D\\uDE80 Ready to Start?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800 mb-4\",\n                                                        children: \"Join thousands of entrepreneurs building profitable businesses without funding. Start your bootstrap journey today.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors\",\n                                                        children: \"Get Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-8 text-center\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/cost-effective-ai-development\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"Cost-Effective AI Development: Build AI Apps on a Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Practical strategies to reduce AI development costs by 70% using free tools and smart resource management.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/build-ai-powered-saas\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"Building AI-Powered SaaS: Technical Architecture Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Step-by-step guide to building scalable AI-powered SaaS applications with best practices.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\bootstrap-lean-startup-2025\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = BootstrapLeanStartup2025;\nvar _c;\n$RefreshReg$(_c, \"BootstrapLeanStartup2025\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/bootstrap-lean-startup-2025/page.tsx\n"));

/***/ })

});