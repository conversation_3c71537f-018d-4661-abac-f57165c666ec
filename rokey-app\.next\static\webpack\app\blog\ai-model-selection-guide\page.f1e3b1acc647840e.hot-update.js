"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/ai-model-selection-guide/page",{

/***/ "(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/blog/ai-model-selection-guide/page.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIModelSelectionGuide)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction AIModelSelectionGuide() {\n    const post = {\n        title: 'Best AI Models 2025: OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro & DeepSeek R1 Compared',\n        author: 'David Okoro',\n        date: '2025-06-25',\n        readTime: '18 min read',\n        category: 'AI Comparison',\n        tags: [\n            'Best AI Models 2025',\n            'OpenAI o3',\n            'Claude 4 Opus',\n            'Gemini 2.5 Pro',\n            'DeepSeek R1',\n            'AI Benchmarks',\n            'Model Performance'\n        ],\n        excerpt: 'Comprehensive comparison of the latest AI models in 2025. Performance benchmarks, cost analysis, coding capabilities, reasoning tests, and multimodal features across OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"AI Model Selection - White robot representing artificial intelligence\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"AI Model Comparison 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape in 2025 has reached unprecedented sophistication. With breakthrough models like OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1 leading the charge, we're witnessing capabilities that seemed impossible just months ago. This comprehensive guide analyzes the latest performance benchmarks, cost structures, and specialized use cases to help you choose the perfect AI model for your needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF What You'll Learn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: [\n                                                            \"• Performance benchmarks across GPQA Diamond, AIME 2024, SWE Bench, and BFCL tests\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 103\n                                                            }, this),\n                                                            \"• Cost analysis per million tokens for input/output\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 72\n                                                            }, this),\n                                                            \"• Specialized capabilities: coding, reasoning, multimodal, and speed\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 89\n                                                            }, this),\n                                                            \"• Real-world use case recommendations for different business needs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFC6 2025 AI Model Champions by Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Reasoning Champion: Gemini 2.5 Pro\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Complex reasoning, mathematical problems, scientific analysis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 86.4% GPQA Diamond (highest reasoning score)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Unmatched performance on complex reasoning tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Exceptional mathematical and scientific problem-solving\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong multimodal capabilities with vision and audio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent context understanding and logical deduction\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $1.25/1M input tokens, $5.00/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Coding Champion: Claude 4 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Software development, code review, debugging, technical documentation\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 72.7% SWE Bench (highest coding score)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Superior code generation across all programming languages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent debugging and code optimization capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong architectural decision-making and best practices\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Exceptional at explaining complex technical concepts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $3.00/1M input tokens, $15.00/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Speed Champion: Llama 4 Scout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Real-time applications, high-throughput processing, latency-sensitive tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 2,600 tokens/second (fastest response time)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Blazing-fast response times for real-time applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent for chatbots and interactive applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good balance of speed and quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Optimized for high-volume concurrent requests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.20/1M input tokens, $0.80/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Cost Champion: Nova Micro\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Budget-conscious applications, high-volume processing, simple tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.04/$0.14 per 1M tokens (most cost-effective)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Extremely cost-effective for large-scale deployments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good performance for simple to medium complexity tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Reliable and consistent output quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Perfect for content generation and basic analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.04/1M input tokens, $0.14/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border-l-4 border-green-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-green-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCA1 RouKey's Smart Advantage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-800\",\n                                                        children: \"Why choose one model when you can have them all? RouKey's intelligent routing automatically selects the best model for each task - Gemini 2.5 Pro for complex reasoning, Claude 4 Sonnet for coding, Llama 4 Scout for speed, and Nova Micro for cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFAF Specialized Use Cases & Model Rankings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83D\\uDCBB Best Models for Coding & Development\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Claude 4 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 72.7% SWE Bench - Best overall for code generation, debugging, and architecture\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. DeepSeek R1:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 71.9% SWE Bench - Excellent for complex algorithms and system design\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 71.7% SWE Bench - Strong at code explanation and refactoring\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. Claude 3.7 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 69.2% SWE Bench - Great for code reviews and documentation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDDE0 Best Models for Complex Reasoning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Gemini 2.5 Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 86.4% GPQA Diamond - Unmatched scientific and mathematical reasoning\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 85.5% GPQA Diamond - Excellent logical deduction and problem-solving\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. Claude 4 Opus:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 84.9% GPQA Diamond - Strong analytical thinking and research\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. DeepSeek R1:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" 84.1% GPQA Diamond - Great for technical analysis and planning\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83C\\uDFA8 Best Models for Creative & Content Writing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Claude 4 Opus:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Superior creative storytelling and narrative development\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent for marketing copy and persuasive writing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. Gemini 2.5 Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Great for technical writing and documentation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. Claude 3.7 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Strong analytical and research-based content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83D\\uDDBC️ Best Models for Multimodal Tasks\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"1. Gemini 2.5 Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Advanced vision, audio, and video processing capabilities\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"2. Claude 4 Opus:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent image analysis and visual reasoning\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"3. OpenAI o3:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Strong multimodal understanding and generation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"4. Llama 4 Vision:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Fast multimodal processing for real-time applications\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83D\\uDCCA 2025 Cost-Performance Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Model\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Input Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Output Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Reasoning Score\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Coding Score\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Speed\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Gemini 2.5 Pro\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$1.25/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$5.00/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-semibold\",\n                                                                            children: \"86.4% \\uD83E\\uDD47\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"68.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Fast\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Claude 4 Sonnet\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$3.00/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$15.00/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"82.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-semibold\",\n                                                                            children: \"72.7% \\uD83E\\uDD47\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Fast\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"OpenAI o3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$15.00/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$60.00/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"85.5%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 256,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"71.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 257,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Medium\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"DeepSeek R1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.55/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$2.19/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 263,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"84.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"71.9%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Fast\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 266,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Llama 4 Scout\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 269,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.20/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.80/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"78.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"65.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-semibold\",\n                                                                            children: \"2600 t/s \\uD83E\\uDD47\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Nova Micro\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-semibold\",\n                                                                            children: \"$0.04/1M \\uD83E\\uDD47\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-green-600 font-semibold\",\n                                                                            children: \"$0.14/1M \\uD83E\\uDD47\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"72.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"58.9%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Very Fast\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border-l-4 border-yellow-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-yellow-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCC8 Performance Notes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-yellow-800\",\n                                                        children: [\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Reasoning Score:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Based on GPQA Diamond benchmark (scientific reasoning)\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 111\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Coding Score:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Based on SWE Bench benchmark (software engineering tasks)\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 111\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Speed:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Tokens per second for real-time applications\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 91\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"\\uD83E\\uDD47 Champions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Best-in-class performance for each category\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFAF 2025 Use Case Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83D\\uDCBC For Startups and Small Businesses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-6 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-6 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Primary:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Nova Micro ($0.04/$0.14 per 1M tokens) - Ultra cost-effective for content generation and basic tasks\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Coding:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" DeepSeek R1 ($0.55/$2.19 per 1M tokens) - Excellent coding performance at budget-friendly prices\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Complex Tasks:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Gemini 2.5 Pro ($1.25/$5.00 per 1M tokens) - Best reasoning capabilities when quality matters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Speed Critical:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Llama 4 Scout ($0.20/$0.80 per 1M tokens) - Fast responses for real-time applications\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-4 text-blue-800 font-medium\",\n                                                        children: \"\\uD83D\\uDCA1 Estimated monthly cost for 10M tokens: $140-500 vs $900+ with premium models\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83C\\uDFE2 For Enterprise Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-6 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-6 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Mission Critical:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Claude 4 Sonnet - Highest reliability and safety for production systems\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Research & Analysis:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Gemini 2.5 Pro - Unmatched reasoning for complex business decisions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Development Teams:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Claude 4 Sonnet + DeepSeek R1 - Complete coding and architecture solutions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"High Volume:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Nova Micro + Llama 4 Scout - Cost optimization for large-scale operations\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-4 text-green-800 font-medium\",\n                                                        children: \"\\uD83D\\uDD12 Enterprise features: Enhanced security, compliance, and dedicated support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83D\\uDE80 For AI-First Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 p-6 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc pl-6 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Multi-Model Strategy:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Use RouKey's intelligent routing across all top models\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Reasoning Tasks:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Gemini 2.5 Pro for scientific and mathematical analysis\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Code Generation:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Claude 4 Sonnet for software development and architecture\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Real-Time Apps:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Llama 4 Scout for chatbots and interactive features\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Cost Optimization:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Automatic fallback to Nova Micro for simple tasks\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-4 text-purple-800 font-medium\",\n                                                        children: \"⚡ Best of all worlds: Premium performance with intelligent cost management\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83D\\uDD2E 2025 AI Trends & What's Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape in 2025 is experiencing unprecedented innovation. Here are the key trends shaping the future:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-blue-900 mb-3\",\n                                                                children: \"\\uD83E\\uDDE0 Reasoning Revolution\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-800\",\n                                                                children: \"Models like Gemini 2.5 Pro are achieving human-level performance on complex scientific reasoning tasks, opening new possibilities for AI-assisted research and analysis.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-green-900 mb-3\",\n                                                                children: \"\\uD83D\\uDCB0 Cost Democratization\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800\",\n                                                                children: \"Ultra-efficient models like Nova Micro are making AI accessible to everyone, with costs dropping 95% while maintaining good performance for most tasks.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-purple-900 mb-3\",\n                                                                children: \"⚡ Speed Breakthroughs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800\",\n                                                                children: \"Real-time AI is here with models like Llama 4 Scout delivering 2,600+ tokens/second, enabling truly interactive AI applications.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-orange-50 to-red-50 p-6 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-orange-900 mb-3\",\n                                                                children: \"\\uD83C\\uDFAF Specialized Excellence\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-orange-800\",\n                                                                children: \"Domain-specific models are achieving superhuman performance in coding, scientific research, and creative tasks, surpassing general-purpose models.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83D\\uDE80 Why RouKey is the Smart Choice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800 mb-4\",\n                                                        children: \"Instead of being locked into one model, RouKey gives you access to ALL the best AI models of 2025 through a single API. Our intelligent routing automatically selects the perfect model for each task - whether you need Gemini 2.5 Pro's reasoning, Claude 4 Sonnet's coding expertise, or Nova Micro's cost efficiency.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/pricing\",\n                                                                className: \"inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors text-center\",\n                                                                children: \"Start Free Trial\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/dashboard\",\n                                                                className: \"inline-block border-2 border-[#ff6b35] text-[#ff6b35] px-6 py-3 rounded-lg font-semibold hover:bg-[#ff6b35] hover:text-white transition-colors text-center\",\n                                                                children: \"Try the Playground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFAF Key Takeaways\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-6 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mr-3 mt-1\",\n                                                                    children: \"✅\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"No single model rules all:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" Gemini 2.5 Pro excels at reasoning, Claude 4 Sonnet dominates coding, Llama 4 Scout wins on speed, and Nova Micro leads on cost.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mr-3 mt-1\",\n                                                                    children: \"✅\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Cost varies dramatically:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" From $0.04 per million tokens (Nova Micro) to $60 per million tokens (OpenAI o3) - choose wisely based on your use case.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mr-3 mt-1\",\n                                                                    children: \"✅\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Performance benchmarks matter:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" Use GPQA Diamond scores for reasoning tasks and SWE Bench scores for coding projects to make informed decisions.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-500 mr-3 mt-1\",\n                                                                    children: \"✅\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Multi-model strategy wins:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" RouKey's intelligent routing gives you the best of all worlds - premium performance with automatic cost optimization.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-8 text-lg text-gray-700\",\n                                                children: \"The AI model landscape in 2025 offers unprecedented capabilities across reasoning, coding, creativity, and cost efficiency. The key to success isn't choosing one model - it's having access to the right model for each specific task. That's exactly what RouKey delivers.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-8 text-center\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/ai-api-gateway-2025-guide\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"The Complete Guide to AI API Gateways in 2025\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Discover how AI API gateways are revolutionizing multi-model routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/cost-effective-ai-development\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"Cost-Effective AI Development: Build AI Apps on a Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Practical strategies to reduce AI development costs by 70% using smart resource management.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = AIModelSelectionGuide;\nvar _c;\n$RefreshReg$(_c, \"AIModelSelectionGuide\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx\n"));

/***/ })

});