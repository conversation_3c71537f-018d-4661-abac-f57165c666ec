"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/ai-model-selection-guide/page",{

/***/ "(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/blog/ai-model-selection-guide/page.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIModelSelectionGuide)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction AIModelSelectionGuide() {\n    const post = {\n        title: 'Best AI Models 2025: OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro & DeepSeek R1 Compared',\n        author: 'David Okoro',\n        date: '2025-06-25',\n        readTime: '18 min read',\n        category: 'AI Comparison',\n        tags: [\n            'Best AI Models 2025',\n            'OpenAI o3',\n            'Claude 4 Opus',\n            'Gemini 2.5 Pro',\n            'DeepSeek R1',\n            'AI Benchmarks',\n            'Model Performance'\n        ],\n        excerpt: 'Comprehensive comparison of the latest AI models in 2025. Performance benchmarks, cost analysis, coding capabilities, reasoning tests, and multimodal features across OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"AI Model Selection - White robot representing artificial intelligence\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"AI Model Comparison 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape in 2025 has reached unprecedented sophistication. With breakthrough models like OpenAI o3, Claude 4 Opus, Gemini 2.5 Pro, and DeepSeek R1 leading the charge, we're witnessing capabilities that seemed impossible just months ago. This comprehensive guide analyzes the latest performance benchmarks, cost structures, and specialized use cases to help you choose the perfect AI model for your needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF What You'll Learn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: [\n                                                            \"• Performance benchmarks across GPQA Diamond, AIME 2024, SWE Bench, and BFCL tests\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 103\n                                                            }, this),\n                                                            \"• Cost analysis per million tokens for input/output\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 72\n                                                            }, this),\n                                                            \"• Specialized capabilities: coding, reasoning, multimodal, and speed\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 89\n                                                            }, this),\n                                                            \"• Real-world use case recommendations for different business needs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"\\uD83C\\uDFC6 2025 AI Model Champions by Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Reasoning Champion: Gemini 2.5 Pro\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Complex reasoning, mathematical problems, scientific analysis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 86.4% GPQA Diamond (highest reasoning score)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Unmatched performance on complex reasoning tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Exceptional mathematical and scientific problem-solving\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong multimodal capabilities with vision and audio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent context understanding and logical deduction\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $1.25/1M input tokens, $5.00/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Coding Champion: Claude 4 Sonnet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Software development, code review, debugging, technical documentation\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 72.7% SWE Bench (highest coding score)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Superior code generation across all programming languages\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent debugging and code optimization capabilities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Strong architectural decision-making and best practices\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Exceptional at explaining complex technical concepts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $3.00/1M input tokens, $15.00/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Speed Champion: Llama 4 Scout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Real-time applications, high-throughput processing, latency-sensitive tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" 2,600 tokens/second (fastest response time)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Blazing-fast response times for real-time applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Excellent for chatbots and interactive applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good balance of speed and quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Optimized for high-volume concurrent requests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.20/1M input tokens, $0.80/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"\\uD83E\\uDD47 Cost Champion: Nova Micro\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Best for:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" Budget-conscious applications, high-volume processing, simple tasks\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Performance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.04/$0.14 per 1M tokens (most cost-effective)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Strengths:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 20\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Extremely cost-effective for large-scale deployments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Good performance for simple to medium complexity tasks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Reliable and consistent output quality\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Perfect for content generation and basic analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Cost:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" $0.04/1M input tokens, $0.14/1M output tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border-l-4 border-green-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-green-900 mb-2\",\n                                                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-800\",\n                                                        children: \"Use RouKey's intelligent routing to automatically select the best model for each task, optimizing for both performance and cost.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Specialized Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Code-Specialized Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Claude 3.5 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Best overall for code generation and debugging\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"GPT-4:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Good for explaining code and architectural decisions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"DeepSeek Coder:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent for specific programming languages\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"Creative Writing Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"GPT-4:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Best for creative storytelling and content creation\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Claude 3.5 Sonnet:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Excellent for technical and analytical writing\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Gemini Pro:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Good for marketing copy and social media content\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Cost-Performance Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full bg-white border border-gray-200 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Model\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Input Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Output Cost\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                                        children: \"Performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"GPT-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 217,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$30/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$60/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Excellent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Claude 3.5 Sonnet\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$3/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$15/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 225,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Excellent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                                            children: \"Gemini 2.0 Flash\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 229,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.075/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 230,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"$0.30/1M\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                                            children: \"Very Good\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                            lineNumber: 232,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Use Case Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"For Startups and Small Businesses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Gemini 2.0 Flash for cost-effective general tasks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Claude 3.5 Sonnet for complex analysis and code\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Fallback:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" GPT-4 for tasks requiring highest quality\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-semibold text-gray-900 mt-8 mb-4\",\n                                                children: \"For Enterprise Applications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Primary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Claude 3.5 Sonnet for reliability and safety\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Secondary:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" GPT-4 for creative and complex reasoning tasks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"High-volume:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Gemini 2.0 Flash for cost optimization\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Future Trends\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model landscape continues to evolve rapidly. Key trends to watch:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Specialized Models:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" More domain-specific models for niche use cases\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Cost Reduction:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Continued price competition driving costs down\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Multimodal Capabilities:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Better integration of text, image, and audio\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Edge Deployment:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" Smaller models optimized for local deployment\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-orange-50 border-l-4 border-orange-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-orange-900 mb-2\",\n                                                        children: \"\\uD83D\\uDE80 Get Started with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-orange-800 mb-4\",\n                                                        children: \"Access all these models through a single API with intelligent routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors\",\n                                                        children: \"Start Free Trial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Choosing the right AI model depends on your specific use case, budget, and performance requirements. The key is to match the model's strengths to your needs while considering cost implications.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"With RouKey's intelligent routing, you don't have to choose just one model. Our system automatically selects the best model for each task, giving you the benefits of all models while optimizing for cost and performance.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-8 text-center\",\n                                    children: \"Related Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/ai-api-gateway-2025-guide\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"The Complete Guide to AI API Gateways in 2025\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Discover how AI API gateways are revolutionizing multi-model routing and cost optimization.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog/cost-effective-ai-development\",\n                                            className: \"group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors\",\n                                                        children: \"Cost-Effective AI Development: Build AI Apps on a Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 text-sm\",\n                                                        children: \"Practical strategies to reduce AI development costs by 70% using smart resource management.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\ai-model-selection-guide\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = AIModelSelectionGuide;\nvar _c;\n$RefreshReg$(_c, \"AIModelSelectionGuide\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/ai-model-selection-guide/page.tsx\n"));

/***/ })

});