"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/page.tsx":
/*!*******************************!*\
  !*** ./src/app/blog/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\n// Blog post data with SEO-optimized content\nconst blogPosts = [\n    {\n        id: 'ai-api-gateway-2025-guide',\n        title: 'The Complete Guide to AI API Gateways in 2025: Multi-Model Routing & Cost Optimization',\n        excerpt: 'Discover how AI API gateways are revolutionizing multi-model routing, reducing costs by 60%, and enabling seamless integration with 300+ AI models. Learn the latest strategies for 2025.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-15',\n        readTime: '8 min read',\n        category: 'AI Technology',\n        tags: [\n            'AI API Gateway',\n            'Multi-Model Routing',\n            'Cost Optimization',\n            'AI Integration',\n            'API Management'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-4636190af475?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: true,\n        seoKeywords: [\n            'AI API gateway 2025',\n            'multi-model routing',\n            'AI cost optimization',\n            'API management tools'\n        ]\n    },\n    {\n        id: 'bootstrap-lean-startup-2025',\n        title: 'How to Bootstrap a Lean Startup in 2025: From $0 to Revenue Without Funding',\n        excerpt: 'Learn the proven strategies to build and scale a lean startup without external funding. Real case studies, actionable frameworks, and growth hacks that work in 2025.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-12',\n        readTime: '12 min read',\n        category: 'Entrepreneurship',\n        tags: [\n            'Bootstrap Startup',\n            'Lean Methodology',\n            'No-Code Tools',\n            'MVP Development',\n            'Revenue Generation'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-47ba0277781c?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: true,\n        seoKeywords: [\n            'bootstrap startup 2025',\n            'lean startup methodology',\n            'startup without funding',\n            'MVP development'\n        ]\n    },\n    {\n        id: 'roukey-ai-routing-strategies',\n        title: 'RouKey\\'s Intelligent AI Routing: How We Achieved 99.9% Uptime with Multi-Provider Fallbacks',\n        excerpt: 'Behind the scenes of RouKey\\'s intelligent routing system. Learn how we built fault-tolerant AI infrastructure that automatically routes to the best-performing models.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-10',\n        readTime: '10 min read',\n        category: 'Product Deep Dive',\n        tags: [\n            'RouKey',\n            'AI Routing',\n            'Fault Tolerance',\n            'Infrastructure',\n            'Reliability'\n        ],\n        image: 'https://plus.unsplash.com/premium_photo-*************-252eab5fd2db?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'AI routing strategies',\n            'multi-provider AI',\n            'AI infrastructure',\n            'fault tolerant AI'\n        ]\n    },\n    {\n        id: 'ai-model-selection-guide',\n        title: 'AI Model Selection Guide 2025: GPT-4, Claude, Gemini, and 300+ Models Compared',\n        excerpt: 'Comprehensive comparison of leading AI models in 2025. Performance benchmarks, cost analysis, and use-case recommendations for developers and businesses.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-08',\n        readTime: '15 min read',\n        category: 'AI Comparison',\n        tags: [\n            'AI Models 2025',\n            'GPT-4',\n            'Claude',\n            'Gemini',\n            'Model Comparison',\n            'AI Benchmarks'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-89b55fcc595e?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'AI model comparison 2025',\n            'best AI models',\n            'GPT-4 vs Claude',\n            'AI model selection'\n        ]\n    },\n    {\n        id: 'build-ai-powered-saas',\n        title: 'Building AI-Powered SaaS: Technical Architecture and Best Practices for 2025',\n        excerpt: 'Step-by-step guide to building scalable AI-powered SaaS applications. Architecture patterns, technology stack recommendations, and real-world implementation strategies.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-05',\n        readTime: '18 min read',\n        category: 'Technical Guide',\n        tags: [\n            'AI SaaS',\n            'Software Architecture',\n            'Scalability',\n            'Best Practices',\n            'Technical Implementation'\n        ],\n        image: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'AI-powered SaaS',\n            'AI software architecture',\n            'scalable AI applications',\n            'SaaS best practices'\n        ]\n    },\n    {\n        id: 'cost-effective-ai-development',\n        title: 'Cost-Effective AI Development: How to Build AI Apps on a Budget in 2025',\n        excerpt: 'Practical strategies to reduce AI development costs by 70%. Free tools, open-source alternatives, and smart resource management for indie developers and startups.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-03',\n        readTime: '14 min read',\n        category: 'Cost Optimization',\n        tags: [\n            'Cost-Effective AI',\n            'Budget Development',\n            'Open Source AI',\n            'Resource Management',\n            'Startup Tips'\n        ],\n        image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'cost-effective AI development',\n            'budget AI projects',\n            'cheap AI tools',\n            'AI development costs'\n        ]\n    }\n];\nconst categories = [\n    'All',\n    'AI Technology',\n    'Entrepreneurship',\n    'Product Deep Dive',\n    'AI Comparison',\n    'Technical Guide',\n    'Cost Optimization'\n];\nfunction BlogPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const filteredPosts = blogPosts.filter((post)=>{\n        const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;\n        const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) || post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) || post.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        return matchesCategory && matchesSearch;\n    });\n    const featuredPosts = blogPosts.filter((post)=>post.featured);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '60px 60px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6\n                                            },\n                                            className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                            children: [\n                                                \"RouKey \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#ff6b35]\",\n                                                    children: \"Blog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 24\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.1\n                                            },\n                                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                            children: \"Insights on AI technology, lean startup methodologies, and building cost-effective solutions. Learn from real-world experiences and industry best practices.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            className: \"max-w-md mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search articles...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    featuredPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-12 text-center\",\n                                    children: \"Featured Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: featuredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.article, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl transition-all duration-300 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.image,\n                                                            alt: post.title,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/20 to-[#f7931e]/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-white/90 text-[#ff6b35] px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: post.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors\",\n                                                            children: post.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6 line-clamp-3\",\n                                                            children: post.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm text-gray-500 mb-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 222,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            post.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatDate(post.date)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 230,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            post.readTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/blog/\".concat(post.id),\n                                                            className: \"inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors\",\n                                                            children: [\n                                                                \"Read More\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, post.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-8 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"px-6 py-2 rounded-full font-medium transition-all duration-200 \".concat(selectedCategory === category ? 'bg-[#ff6b35] text-white shadow-lg' : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-12 text-center\",\n                                    children: selectedCategory === 'All' ? 'All Articles' : \"\".concat(selectedCategory, \" Articles\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: filteredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.article, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.05\n                                            },\n                                            className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.image,\n                                                            alt: post.title,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/20 to-[#f7931e]/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-white/90 text-[#ff6b35] px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: post.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors line-clamp-2\",\n                                                            children: post.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-3 text-sm\",\n                                                            children: post.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs text-gray-500 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        formatDate(post.date)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        post.readTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-4\",\n                                                            children: post.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs\",\n                                                                    children: tag\n                                                                }, tag, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/blog/\".concat(post.id),\n                                                            className: \"inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors text-sm\",\n                                                            children: [\n                                                                \"Read More\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-3 w-3 ml-1 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, post.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                filteredPosts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 text-lg\",\n                                        children: \"No articles found matching your criteria.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPage, \"pI01Q/l1cGXnhBU+IE0+8yyKvu8=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/page.tsx\n"));

/***/ })

});