"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/page",{

/***/ "(app-pages-browser)/./src/app/blog/page.tsx":
/*!*******************************!*\
  !*** ./src/app/blog/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BlogPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,MagnifyingGlassIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,MagnifyingGlassIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,MagnifyingGlassIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,MagnifyingGlassIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,MagnifyingGlassIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CalendarIcon,ClockIcon,MagnifyingGlassIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\n// Blog post data with SEO-optimized content\nconst blogPosts = [\n    {\n        id: 'ai-api-gateway-2025-guide',\n        title: 'The Complete Guide to AI API Gateways in 2025: Multi-Model Routing & Cost Optimization',\n        excerpt: 'Discover how AI API gateways are revolutionizing multi-model routing, reducing costs by 60%, and enabling seamless integration with 300+ AI models. Learn the latest strategies for 2025.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-15',\n        readTime: '8 min read',\n        category: 'AI Technology',\n        tags: [\n            'AI API Gateway',\n            'Multi-Model Routing',\n            'Cost Optimization',\n            'AI Integration',\n            'API Management'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-4636190af475?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: true,\n        seoKeywords: [\n            'AI API gateway 2025',\n            'multi-model routing',\n            'AI cost optimization',\n            'API management tools'\n        ]\n    },\n    {\n        id: 'bootstrap-lean-startup-2025',\n        title: 'How to Bootstrap a Lean Startup in 2025: From $0 to Revenue Without Funding',\n        excerpt: 'Learn the proven strategies to build and scale a lean startup without external funding. Real case studies, actionable frameworks, and growth hacks that work in 2025.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-12',\n        readTime: '12 min read',\n        category: 'Entrepreneurship',\n        tags: [\n            'Bootstrap Startup',\n            'Lean Methodology',\n            'No-Code Tools',\n            'MVP Development',\n            'Revenue Generation'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-47ba0277781c?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: true,\n        seoKeywords: [\n            'bootstrap startup 2025',\n            'lean startup methodology',\n            'startup without funding',\n            'MVP development'\n        ]\n    },\n    {\n        id: 'roukey-ai-routing-strategies',\n        title: 'RouKey\\'s Intelligent AI Routing: How We Achieved 99.9% Uptime with Multi-Provider Fallbacks',\n        excerpt: 'Behind the scenes of RouKey\\'s intelligent routing system. Learn how we built fault-tolerant AI infrastructure that automatically routes to the best-performing models.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-10',\n        readTime: '10 min read',\n        category: 'Product Deep Dive',\n        tags: [\n            'RouKey',\n            'AI Routing',\n            'Fault Tolerance',\n            'Infrastructure',\n            'Reliability'\n        ],\n        image: 'https://plus.unsplash.com/premium_photo-*************-252eab5fd2db?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'AI routing strategies',\n            'multi-provider AI',\n            'AI infrastructure',\n            'fault tolerant AI'\n        ]\n    },\n    {\n        id: 'ai-model-selection-guide',\n        title: 'AI Model Selection Guide 2025: GPT-4, Claude, Gemini, and 300+ Models Compared',\n        excerpt: 'Comprehensive comparison of leading AI models in 2025. Performance benchmarks, cost analysis, and use-case recommendations for developers and businesses.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-08',\n        readTime: '15 min read',\n        category: 'AI Comparison',\n        tags: [\n            'AI Models 2025',\n            'GPT-4',\n            'Claude',\n            'Gemini',\n            'Model Comparison',\n            'AI Benchmarks'\n        ],\n        image: 'https://images.unsplash.com/photo-*************-89b55fcc595e?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'AI model comparison 2025',\n            'best AI models',\n            'GPT-4 vs Claude',\n            'AI model selection'\n        ]\n    },\n    {\n        id: 'build-ai-powered-saas',\n        title: 'Building AI-Powered SaaS: Technical Architecture and Best Practices for 2025',\n        excerpt: 'Step-by-step guide to building scalable AI-powered SaaS applications. Architecture patterns, technology stack recommendations, and real-world implementation strategies.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-05',\n        readTime: '18 min read',\n        category: 'Technical Guide',\n        tags: [\n            'AI SaaS',\n            'Software Architecture',\n            'Scalability',\n            'Best Practices',\n            'Technical Implementation'\n        ],\n        image: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'AI-powered SaaS',\n            'AI software architecture',\n            'scalable AI applications',\n            'SaaS best practices'\n        ]\n    },\n    {\n        id: 'cost-effective-ai-development',\n        title: 'Cost-Effective AI Development: How to Build AI Apps on a Budget in 2025',\n        excerpt: 'Practical strategies to reduce AI development costs by 70%. Free tools, open-source alternatives, and smart resource management for indie developers and startups.',\n        content: 'Full blog content here...',\n        author: 'David Okoro',\n        date: '2025-01-03',\n        readTime: '14 min read',\n        category: 'Cost Optimization',\n        tags: [\n            'Cost-Effective AI',\n            'Budget Development',\n            'Open Source AI',\n            'Resource Management',\n            'Startup Tips'\n        ],\n        image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?fm=jpg&q=80&w=800&ixlib=rb-4.1.0',\n        featured: false,\n        seoKeywords: [\n            'cost-effective AI development',\n            'budget AI projects',\n            'cheap AI tools',\n            'AI development costs'\n        ]\n    }\n];\nconst categories = [\n    'All',\n    'AI Technology',\n    'Entrepreneurship',\n    'Product Deep Dive',\n    'AI Comparison',\n    'Technical Guide',\n    'Cost Optimization'\n];\nfunction BlogPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        if (searchTerm.trim()) {\n            // Scroll to results section\n            const resultsSection = document.getElementById('search-results');\n            if (resultsSection) {\n                resultsSection.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'start'\n                });\n            }\n        }\n    };\n    const filteredPosts = blogPosts.filter((post)=>{\n        const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;\n        const matchesSearch = searchTerm === '' || post.title.toLowerCase().includes(searchTerm.toLowerCase()) || post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) || post.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        return matchesCategory && matchesSearch;\n    });\n    const featuredPosts = blogPosts.filter((post)=>post.featured);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"\\n                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                \",\n                                        backgroundSize: '60px 60px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6\n                                            },\n                                            className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                            children: [\n                                                \"RouKey \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#ff6b35]\",\n                                                    children: \"Blog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 24\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.1\n                                            },\n                                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                            children: \"Insights on AI technology, lean startup methodologies, and building cost-effective solutions. Learn from real-world experiences and industry best practices.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 0.2\n                                            },\n                                            className: \"max-w-md mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSearchSubmit,\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search articles by title, content, or tags...\",\n                                                            value: searchTerm,\n                                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                                            className: \"w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all duration-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setSearchTerm(''),\n                                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this),\n                                                searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 text-sm text-gray-600 text-center\",\n                                                    children: [\n                                                        filteredPosts.length,\n                                                        \" article\",\n                                                        filteredPosts.length !== 1 ? 's' : '',\n                                                        ' found for \"',\n                                                        searchTerm,\n                                                        '\"',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"block text-xs text-gray-400 mt-1\",\n                                                            children: \"Press Enter to view results\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    featuredPosts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-12 text-center\",\n                                    children: \"Featured Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: featuredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.article, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden hover:shadow-2xl transition-all duration-300 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.image,\n                                                            alt: post.title,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/20 to-[#f7931e]/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-white/90 text-[#ff6b35] px-3 py-1 rounded-full text-sm font-medium\",\n                                                                children: post.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors\",\n                                                            children: post.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6 line-clamp-3\",\n                                                            children: post.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-sm text-gray-500 mb-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 254,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            post.author\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            formatDate(post.date)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            post.readTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/blog/\".concat(post.id),\n                                                            className: \"inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors\",\n                                                            children: [\n                                                                \"Read More\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, post.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-8 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedCategory(category),\n                                        className: \"px-6 py-2 rounded-full font-medium transition-all duration-200 \".concat(selectedCategory === category ? 'bg-[#ff6b35] text-white shadow-lg' : 'bg-white text-gray-600 hover:bg-gray-100 border border-gray-200'),\n                                        children: category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"search-results\",\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[1400px] mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-12 text-center\",\n                                    children: searchTerm ? 'Search Results for \"'.concat(searchTerm, '\"') : selectedCategory === 'All' ? 'All Articles' : \"\".concat(selectedCategory, \" Articles\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: filteredPosts.map((post, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.article, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.05\n                                            },\n                                            className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-video relative overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.image,\n                                                            alt: post.title,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/20 to-[#f7931e]/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-white/90 text-[#ff6b35] px-2 py-1 rounded-full text-xs font-medium\",\n                                                                children: post.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors line-clamp-2\",\n                                                            children: post.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4 line-clamp-3 text-sm\",\n                                                            children: post.excerpt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between text-xs text-gray-500 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        formatDate(post.date)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        post.readTime\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-4\",\n                                                            children: post.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs\",\n                                                                    children: tag\n                                                                }, tag, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/blog/\".concat(post.id),\n                                                            className: \"inline-flex items-center text-[#ff6b35] font-semibold hover:text-[#e55a2b] transition-colors text-sm\",\n                                                            children: [\n                                                                \"Read More\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3 ml-1 group-hover:translate-x-1 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, post.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                filteredPosts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CalendarIcon_ClockIcon_MagnifyingGlassIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-lg mb-2\",\n                                            children: \"No articles found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    \"Try searching for different keywords or \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSearchTerm(''),\n                                                        className: \"text-[#ff6b35] hover:underline\",\n                                                        children: \"clear your search\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 63\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : 'Try selecting a different category'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\page.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogPage, \"pI01Q/l1cGXnhBU+IE0+8yyKvu8=\");\n_c = BlogPage;\nvar _c;\n$RefreshReg$(_c, \"BlogPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/page.tsx\n"));

/***/ })

});