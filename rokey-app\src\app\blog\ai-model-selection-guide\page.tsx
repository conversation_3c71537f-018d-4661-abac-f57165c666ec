'use client';

import { motion } from 'framer-motion';
import { CalendarIcon, ClockIcon, UserIcon } from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import Link from 'next/link';

// Helper function for consistent date formatting
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export default function AIModelSelectionGuide() {
  const post = {
    title: 'AI Model Selection Guide 2025: GPT-4, <PERSON>, Gemini, and 300+ Models Compared',
    author: '<PERSON>',
    date: '2025-01-08',
    readTime: '15 min read',
    category: 'AI Comparison',
    tags: ['AI Models 2025', 'GPT-4', 'Claude', 'Gemini', 'Model Comparison', 'AI Benchmarks'],
    excerpt: 'Comprehensive comparison of leading AI models in 2025. Performance benchmarks, cost analysis, and use-case recommendations for developers and businesses.'
  };

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="mb-6">
                <Link href="/blog" className="text-[#ff6b35] hover:text-[#e55a2b] font-medium">
                  ← Back to Blog
                </Link>
              </div>
              
              <div className="mb-6">
                <span className="bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium">
                  {post.category}
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                {post.title}
              </h1>

              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {post.excerpt}
              </p>

              <div className="flex items-center space-x-6 text-sm text-gray-500 mb-8">
                <div className="flex items-center">
                  <UserIcon className="h-4 w-4 mr-2" />
                  {post.author}
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {formatDate(post.date)}
                </div>
                <div className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-2" />
                  {post.readTime}
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-8">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Article Content */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.article
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              <div className="aspect-video rounded-2xl mb-12 relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1485827404703-89b55fcc595e?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                  alt="AI Model Selection - White robot representing artificial intelligence"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <h2 className="text-white text-2xl font-bold text-center px-8">
                    AI Model Comparison 2025
                  </h2>
                </div>
              </div>

              <div className="text-gray-800 space-y-6 text-lg leading-relaxed">
                <p>
                  The AI model landscape in 2025 is more diverse and capable than ever before. With over 300 models available across different providers, choosing the right model for your specific use case can be overwhelming. This comprehensive guide breaks down the leading AI models, their strengths, weaknesses, and optimal use cases.
                </p>

                <div className="bg-blue-50 border-l-4 border-blue-500 p-6 my-8">
                  <h3 className="text-xl font-semibold text-blue-900 mb-2">🎯 Quick Navigation</h3>
                  <p className="text-blue-800">
                    This guide covers performance benchmarks, cost analysis, and specific recommendations for coding, creative writing, analysis, and general-purpose tasks.
                  </p>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Top-Tier Models: The Leaders</h2>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">GPT-4 (OpenAI)</h3>
                <p><strong>Best for:</strong> General-purpose tasks, creative writing, complex reasoning</p>
                <p><strong>Strengths:</strong></p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Excellent at following complex instructions</li>
                  <li>Strong creative writing capabilities</li>
                  <li>Good at maintaining context in long conversations</li>
                  <li>Reliable and consistent performance</li>
                </ul>
                <p><strong>Cost:</strong> $30/1M input tokens, $60/1M output tokens</p>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">Claude 3.5 Sonnet (Anthropic)</h3>
                <p><strong>Best for:</strong> Code generation, analysis, technical writing</p>
                <p><strong>Strengths:</strong></p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Superior code generation and debugging</li>
                  <li>Excellent at analytical tasks</li>
                  <li>Strong safety and alignment</li>
                  <li>Good at handling large documents</li>
                </ul>
                <p><strong>Cost:</strong> $3/1M input tokens, $15/1M output tokens</p>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">Gemini 2.0 Flash (Google)</h3>
                <p><strong>Best for:</strong> Fast responses, cost-effective tasks, multimodal</p>
                <p><strong>Strengths:</strong></p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Extremely fast response times</li>
                  <li>Cost-effective for high-volume use</li>
                  <li>Good multimodal capabilities</li>
                  <li>Reliable for simple to medium complexity tasks</li>
                </ul>
                <p><strong>Cost:</strong> $0.075/1M input tokens, $0.30/1M output tokens</p>

                <div className="bg-green-50 border-l-4 border-green-500 p-6 my-8">
                  <h3 className="text-xl font-semibold text-green-900 mb-2">💡 Pro Tip</h3>
                  <p className="text-green-800">
                    Use RouKey's intelligent routing to automatically select the best model for each task, optimizing for both performance and cost.
                  </p>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Specialized Models</h2>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">Code-Specialized Models</h3>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>Claude 3.5 Sonnet:</strong> Best overall for code generation and debugging</li>
                  <li><strong>GPT-4:</strong> Good for explaining code and architectural decisions</li>
                  <li><strong>DeepSeek Coder:</strong> Excellent for specific programming languages</li>
                </ul>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">Creative Writing Models</h3>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>GPT-4:</strong> Best for creative storytelling and content creation</li>
                  <li><strong>Claude 3.5 Sonnet:</strong> Excellent for technical and analytical writing</li>
                  <li><strong>Gemini Pro:</strong> Good for marketing copy and social media content</li>
                </ul>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Cost-Performance Analysis</h2>

                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Input Cost</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Output Cost</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">GPT-4</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$30/1M</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$60/1M</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Excellent</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Claude 3.5 Sonnet</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$3/1M</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$15/1M</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Excellent</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Gemini 2.0 Flash</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$0.075/1M</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$0.30/1M</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Very Good</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Use Case Recommendations</h2>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">For Startups and Small Businesses</h3>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>Primary:</strong> Gemini 2.0 Flash for cost-effective general tasks</li>
                  <li><strong>Secondary:</strong> Claude 3.5 Sonnet for complex analysis and code</li>
                  <li><strong>Fallback:</strong> GPT-4 for tasks requiring highest quality</li>
                </ul>

                <h3 className="text-2xl font-semibold text-gray-900 mt-8 mb-4">For Enterprise Applications</h3>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>Primary:</strong> Claude 3.5 Sonnet for reliability and safety</li>
                  <li><strong>Secondary:</strong> GPT-4 for creative and complex reasoning tasks</li>
                  <li><strong>High-volume:</strong> Gemini 2.0 Flash for cost optimization</li>
                </ul>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Future Trends</h2>

                <p>The AI model landscape continues to evolve rapidly. Key trends to watch:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li><strong>Specialized Models:</strong> More domain-specific models for niche use cases</li>
                  <li><strong>Cost Reduction:</strong> Continued price competition driving costs down</li>
                  <li><strong>Multimodal Capabilities:</strong> Better integration of text, image, and audio</li>
                  <li><strong>Edge Deployment:</strong> Smaller models optimized for local deployment</li>
                </ul>

                <div className="bg-orange-50 border-l-4 border-orange-500 p-6 my-8">
                  <h3 className="text-xl font-semibold text-orange-900 mb-2">🚀 Get Started with RouKey</h3>
                  <p className="text-orange-800 mb-4">
                    Access all these models through a single API with intelligent routing and cost optimization.
                  </p>
                  <Link 
                    href="/pricing" 
                    className="inline-block bg-[#ff6b35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors"
                  >
                    Start Free Trial
                  </Link>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Conclusion</h2>

                <p>
                  Choosing the right AI model depends on your specific use case, budget, and performance requirements. The key is to match the model's strengths to your needs while considering cost implications.
                </p>

                <p>
                  With RouKey's intelligent routing, you don't have to choose just one model. Our system automatically selects the best model for each task, giving you the benefits of all models while optimizing for cost and performance.
                </p>
              </div>
            </motion.article>
          </div>
        </section>

        {/* Related Posts */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Related Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Link href="/blog/ai-api-gateway-2025-guide" className="group">
                <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors">
                    The Complete Guide to AI API Gateways in 2025
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Discover how AI API gateways are revolutionizing multi-model routing and cost optimization.
                  </p>
                </div>
              </Link>
              <Link href="/blog/cost-effective-ai-development" className="group">
                <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#ff6b35] transition-colors">
                    Cost-Effective AI Development: Build AI Apps on a Budget
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Practical strategies to reduce AI development costs by 70% using smart resource management.
                  </p>
                </div>
              </Link>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
